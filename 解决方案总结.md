# 微信支付平台证书切换公钥模式 - 解决方案总结

## 问题原因
您遇到的错误 `RESOURCE_NOT_EXISTS` 是因为微信支付平台证书已被废弃，需要切换到微信支付公钥模式。

## 已完成的代码修改

### 1. 配置文件修改 (wxpay.properties)
```properties
# 新增配置
wxpay.public-key-path=pub_key.pem
wxpay.public-key-id=PUB_KEY_ID_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
```

### 2. 配置类修改 (WxPayConfig.java)
- ✅ 添加公钥相关属性
- ✅ 新增 `getPublicKey()` 方法
- ✅ 修改 `getVerifier()` 使用公钥验证
- ✅ 新增 `getWxPayPublicKeyId()` Bean

### 3. 服务类修改 (WxPayServiceImpl.java)
- ✅ 注入公钥ID
- ✅ 为所有HTTP请求添加 `Wechatpay-Serial` 头

## 您需要完成的步骤

### 第一步：获取微信支付公钥
1. 登录微信支付商户平台 (https://pay.weixin.qq.com)
2. 进入 **账户中心** → **API安全**
3. 找到 **微信支付公钥** 选项，点击 **申请公钥**
4. 下载公钥文件，保存为 `pub_key.pem`
5. 将文件放到项目根目录（与apiclient_key.pem同级）
6. 记录公钥ID（格式：PUB_KEY_ID_xxxxxxxxxx）

### 第二步：更新配置
修改 `src/main/resources/wxpay.properties` 中的公钥ID：
```properties
wxpay.public-key-id=您从商户平台获取的实际公钥ID
```

### 第三步：重新编译和启动
```bash
mvn clean compile
mvn spring-boot:run
```

### 第四步：在商户平台启动切换
1. 确保应用启动成功
2. 登录商户平台 → API安全 → 验签方式更换
3. 点击 **开始更换**
4. 输入操作密码和验证码
5. 监控切换进度（需要7天完全切换）

## 验证成功的标志

### 1. 应用启动成功
- 不再出现 `RESOURCE_NOT_EXISTS` 错误
- 看到日志：`微信支付客户端初始化完成，使用公钥模式`

### 2. 功能正常
- 能够创建订单
- 能够查询订单状态
- 回调验证正常

### 3. 商户平台显示
- 回调使用公钥比例逐步增加
- 应答使用公钥比例逐步增加

## 技术细节说明

### 1. 验证器实现
使用自定义Verifier实现，直接使用公钥进行SHA256withRSA签名验证。

### 2. HTTP请求头
所有微信支付API请求都会自动添加：
```
Wechatpay-Serial: PUB_KEY_ID_xxxxxxxxxx
```

### 3. 回调兼容
现有的回调验证器已经支持公钥模式，无需额外修改。

## 注意事项

1. **公钥ID格式**：必须包含 `PUB_KEY_ID_` 前缀
2. **文件位置**：`pub_key.pem` 必须在项目根目录
3. **渐进切换**：商户平台的切换是渐进式的，需要7天
4. **不可回退**：完全作废平台证书后不可回退

## 常见问题

### Q: 启动时仍然报证书错误？
A: 检查公钥文件路径和公钥ID配置是否正确。

### Q: 如何确认公钥ID正确？
A: 公钥ID在商户平台下载公钥时显示，格式为 `PUB_KEY_ID_` 开头。

### Q: 切换过程中出现问题怎么办？
A: 可以在商户平台点击"停止更换"回退到平台证书模式。

## 联系支持

如果遇到问题：
1. 检查完整的错误日志
2. 确认公钥文件和配置正确
3. 联系微信支付技术支持

---

**重要提醒**：请务必先在商户平台获取公钥文件和公钥ID，然后更新配置文件中的公钥ID，最后重启应用测试。
