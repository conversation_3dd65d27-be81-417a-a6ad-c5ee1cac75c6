# 测试微信支付公钥配置

## 完成的修改总结

### 1. 配置文件修改
- ✅ `wxpay.properties` - 添加了公钥路径和公钥ID配置
- ✅ `WxPayConfig.java` - 从平台证书模式切换到公钥模式
- ✅ `WxPayServiceImpl.java` - 为所有HTTP请求添加了Wechatpay-Serial头

### 2. 具体修改内容

#### wxpay.properties 新增配置：
```properties
# 微信支付公钥文件路径
wxpay.public-key-path=pub_key.pem
# 微信支付公钥ID（需要从商户平台获取）
wxpay.public-key-id=PUB_KEY_ID_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
```

#### WxPayConfig.java 主要修改：
1. 新增公钥相关属性
2. 添加 `getPublicKey()` 方法加载公钥
3. 修改 `getVerifier()` 方法使用公钥验证
4. 新增 `getWxPayPublicKeyId()` Bean

#### WxPayServiceImpl.java 主要修改：
1. 注入公钥ID
2. 为所有HTTP请求添加 `Wechatpay-Serial` 头

### 3. 需要您完成的步骤

#### 步骤1：获取微信支付公钥
1. 登录微信支付商户平台
2. 进入 **账户中心** → **API安全**
3. 点击 **申请公钥**
4. 下载公钥文件，重命名为 `pub_key.pem`
5. 将文件放置到项目根目录（与apiclient_key.pem同级）
6. 记录公钥ID（格式：PUB_KEY_ID_xxxxxxxxxx）

#### 步骤2：更新配置
修改 `src/main/resources/wxpay.properties` 中的公钥ID：
```properties
wxpay.public-key-id=您的实际公钥ID
```

#### 步骤3：测试应用启动
```bash
mvn clean compile
mvn spring-boot:run
```

#### 步骤4：检查启动日志
查看是否有以下成功日志：
```
微信支付客户端初始化完成，使用公钥模式，公钥ID: PUB_KEY_ID_xxxxx
```

#### 步骤5：在商户平台启动切换
1. 确保应用启动成功
2. 登录商户平台 → API安全
3. 点击 **验签方式更换**
4. 点击 **开始更换**
5. 监控切换进度

### 4. 验证方法

#### 方法1：检查应用启动
- 应用应该能够正常启动，不再出现证书相关错误

#### 方法2：测试支付功能
- 创建订单
- 生成支付二维码
- 查询订单状态

#### 方法3：检查HTTP请求头
在日志中应该能看到所有微信支付API请求都包含：
```
Wechatpay-Serial: PUB_KEY_ID_xxxxxxxxxx
```

### 5. 常见问题排查

#### 问题1：公钥文件不存在
```
错误：公钥文件不存在
解决：确保pub_key.pem文件在项目根目录
```

#### 问题2：公钥ID格式错误
```
错误：公钥ID格式不正确
解决：确保公钥ID以PUB_KEY_ID_开头
```

#### 问题3：仍然报证书错误
```
错误：仍然尝试下载平台证书
解决：检查是否正确注入了公钥ID，重启应用
```

### 6. 回退方案

如果遇到问题需要回退：
1. 在商户平台点击 **停止更换**
2. 恢复原来的配置文件
3. 重启应用

### 7. 联系支持

如果问题无法解决：
1. 查看完整的错误日志
2. 联系微信支付技术支持
3. 提供商户号和具体错误信息
