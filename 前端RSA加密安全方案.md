# 前端RSA加密安全方案

## 问题背景

您提出了一个重要的安全问题：前端填写的表单信息传给后端时，如果在网络传输过程中没有加密，敏感信息就存在被截获的风险。

## 解决方案

我们实现了一个**前端RSA加密 + 后端智能处理**的安全方案：

### 1. 前端加密实现

#### 1.1 引入RSA加密库
```html
<!-- RSA加密库 -->
<script src="https://cdn.jsdelivr.net/npm/jsencrypt@3.3.2/bin/jsencrypt.min.js"></script>
```

#### 1.2 动态获取公钥
- 前端从后端API获取微信支付公钥：`GET /api/wx-pay/public-key`
- 确保使用最新的有效公钥进行加密

#### 1.3 敏感字段识别
自动识别并加密以下敏感字段：
- `contactName` - 联系人姓名
- `mobilePhone` - 手机号码
- `contactEmail` - 邮箱地址
- `idCardName` - 身份证姓名
- `idCardNumber` - 身份证号码
- `legalPerson` - 法定代表人
- `beneficiaryName` - 受益人姓名
- `beneficiaryIdNumber` - 受益人身份证号
- `beneficiaryAddress` - 受益人地址
- `accountName` - 账户名称
- `accountNumber` - 银行账号
- `agentIdNumber` - 经办人身份证号
- `agentName` - 经办人姓名

#### 1.4 智能加密处理
```javascript
// RSA加密工具类
class RSAEncryption {
    // 自动初始化公钥
    async initialize() { ... }
    
    // 单个字符串加密
    async encryptString(plainText) { ... }
    
    // 批量敏感数据加密
    async encryptSensitiveData(data) { ... }
}
```

### 2. 后端智能处理

#### 2.1 加密数据识别
```java
private boolean isBase64Encrypted(String value) {
    // 启发式判断是否为加密数据
    // 1. 长度检查（加密后数据较长）
    // 2. Base64格式检查
    // 3. 长度特征检查
    return value.matches("^[A-Za-z0-9+/]*={0,2}$") && value.length() > 200;
}
```

#### 2.2 智能处理逻辑
```java
private Map<String, String> decryptSensitiveData(Map<String, String> body) {
    for (String field : sensitiveFields) {
        String value = body.get(field);
        if (isBase64Encrypted(value)) {
            // 前端已加密，直接使用
            log.info("字段 {} 已由前端加密", field);
        } else {
            // 前端未加密，后端进行加密
            String encrypted = wxPayConfig.encryptSensitiveInfo(value);
            body.put(field, encrypted);
            log.info("字段 {} 在后端进行了加密", field);
        }
    }
    return body;
}
```

### 3. 安全传输流程

```
前端表单数据 → RSA公钥加密 → HTTPS/HTTP传输 → 后端智能处理 → 微信支付API
     ↓              ↓              ↓              ↓              ↓
  明文敏感数据    加密敏感数据    网络传输安全    验证并处理    最终加密数据
```

### 4. 安全优势

#### 4.1 多层防护
1. **前端加密**：敏感数据在离开浏览器前就已加密
2. **网络传输**：即使使用HTTP，敏感数据也是加密的
3. **后端验证**：智能识别加密状态，确保数据安全

#### 4.2 兼容性保障
- 前端加密失败时，后端自动进行加密
- 支持渐进式部署，不影响现有功能
- 向后兼容未加密的客户端

#### 4.3 性能优化
- 并行加密多个字段
- 智能跳过非敏感字段
- 长度检查避免无效加密

### 5. 使用示例

#### 5.1 商家入驻申请
```javascript
// 自动加密敏感数据
const encryptedData = await rsaEncryption.encryptSensitiveData(requestData);

// 添加公钥ID头
const headers = {
    'Content-Type': 'application/json',
    'Wechatpay-Serial': RSA_CONFIG.publicKeyId
};

// 安全传输
fetch('/api/wx-pay/applyment', {
    method: 'POST',
    headers: headers,
    body: JSON.stringify(encryptedData)
});
```

#### 5.2 后端处理
```java
@PostMapping("/applyment")
public AjaxResult applyment(@RequestBody Map<String, String> body) {
    // 智能解密/加密处理
    Map<String, String> processedBody = decryptSensitiveData(body);
    
    // 发送到微信支付
    String result = wxPayService.applyment(processedBody);
    return AjaxResult.success(result);
}
```

### 6. 测试验证

我们提供了专门的测试页面 `ui/encryption-test.html`：

1. **公钥获取测试** - 验证能否正确获取公钥
2. **加密功能测试** - 测试单个字符串加密
3. **批量加密测试** - 测试多字段批量加密
4. **网络传输测试** - 模拟安全传输过程

### 7. 部署说明

#### 7.1 前端部署
1. 确保引入了JSEncrypt库
2. 更新了demo.html中的加密代码
3. 页面加载时自动初始化RSA加密

#### 7.2 后端部署
1. 添加了公钥获取API接口
2. 更新了商家入驻申请处理逻辑
3. 确保微信支付公钥配置正确

### 8. 安全建议

#### 8.1 生产环境建议
- 使用HTTPS协议进行双重保护
- 定期更新微信支付公钥
- 监控加密失败的情况

#### 8.2 进一步优化
- 可以考虑添加数据完整性校验
- 实现加密数据的缓存机制
- 添加更详细的安全日志

## 总结

通过这个方案，我们解决了您提出的安全问题：

1. **敏感数据在网络传输中是加密的**，即使使用HTTP也是安全的
2. **前后端智能协作**，确保数据始终处于加密状态
3. **向后兼容**，不影响现有功能的正常使用
4. **易于测试和验证**，提供了完整的测试工具

这样，即使在HTTP传输过程中，敏感信息也无法被恶意截获和解读，大大提升了系统的安全性。
