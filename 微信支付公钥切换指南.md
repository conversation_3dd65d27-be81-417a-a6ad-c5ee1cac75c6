# 微信支付公钥切换指南

## 问题描述
您遇到的错误是因为微信支付平台证书已被废弃，需要切换到微信支付公钥模式。

## 解决步骤

### 第一步：在微信支付商户平台申请公钥

1. 登录微信支付商户平台 (https://pay.weixin.qq.com)
2. 进入 **账户中心** → **API安全**
3. 找到 **微信支付公钥** 选项
4. 点击 **申请公钥**
5. 下载微信支付公钥文件，保存为 `pub_key.pem`
6. 记录公钥ID（格式类似：`PUB_KEY_ID_xxxxxxxxxxxxxxxxxxxxxxxxx`）

### 第二步：放置公钥文件

将下载的 `pub_key.pem` 文件放置到项目根目录下（与 `apiclient_key.pem` 同级）

### 第三步：更新配置文件

已经为您修改了 `src/main/resources/wxpay.properties` 文件，请将以下配置中的公钥ID替换为您从商户平台获取的实际公钥ID：

```properties
# 微信支付公钥ID（需要替换为实际的公钥ID）
wxpay.public-key-id=PUB_KEY_ID_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
```

### 第四步：代码修改说明

已经为您修改了 `WxPayConfig.java` 文件，主要变更：

1. **新增配置属性**：
   - `publicKeyPath`: 公钥文件路径
   - `publicKeyId`: 公钥ID

2. **修改验证器**：
   - 从使用 `CertificatesManager` 切换到直接使用公钥验证
   - 实现了基于公钥的签名验证逻辑

3. **新增公钥加载方法**：
   - `getPublicKey()`: 加载微信支付公钥

### 第五步：在商户平台启动切换

1. 确保代码修改完成并测试通过
2. 登录商户平台，进入 **账户中心** → **API安全**
3. 找到 **验签方式更换** 选项
4. 点击 **开始更换**
5. 输入操作密码和手机验证码
6. 监控切换进度（分为回调和应答两个进度条）

### 第六步：测试验证

1. 重新启动应用
2. 测试微信支付相关功能
3. 确保没有证书相关错误

## 注意事项

1. **公钥ID格式**：确保公钥ID包含 `PUB_KEY_ID_` 前缀
2. **文件路径**：确保 `pub_key.pem` 文件路径正确
3. **灰度切换**：商户平台的切换是渐进式的，需要7天时间完全切换
4. **兼容性**：在切换期间，系统会同时支持平台证书和公钥模式

## 常见问题

### Q: 如何获取公钥ID？
A: 在商户平台下载公钥时，页面会显示公钥ID，格式为 `PUB_KEY_ID_` 开头的字符串。

### Q: 切换后还能回退吗？
A: 在完全作废平台证书之前可以回退，作废后不可回退。

### Q: 如何确认切换成功？
A: 应用启动时不再出现证书相关错误，且微信支付功能正常工作。

## 技术支持

如果遇到问题，可以：
1. 查看微信支付官方文档
2. 联系微信支付技术支持
3. 检查日志中的详细错误信息
