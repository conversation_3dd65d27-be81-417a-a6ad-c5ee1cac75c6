<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线支付系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入二维码生成库 -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.1/build/qrcode.min.js"></script>
    <!-- RSA加密库 -->
    <script src="https://cdn.jsdelivr.net/npm/jsencrypt@3.3.2/bin/jsencrypt.min.js"></script>

    <!-- Tailwind 配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        neutral: '#1F2937',
                    },
                    fontFamily: {
                        inter: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>

    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .btn-hover {
                @apply transform transition-all duration-300 hover:scale-105 hover:shadow-lg active:scale-95;
            }
            .qrcode-container {
                @apply relative overflow-hidden rounded-xl bg-white shadow-md transition-all duration-500 flex items-center justify-center;
            }
            .payment-option.selected {
                @apply border-blue-500 bg-blue-50 shadow-lg;
            }
            .payment-option img {
                @apply transition-all duration-300;
            }
            .payment-option:hover {
                @apply transform scale-105;
            }

            /* 步骤指示器样式 */
            .step-content {
                min-height: 400px;
            }

            .step-indicator {
                transition: all 0.3s ease;
            }

            .step-indicator.active {
                transform: scale(1.1);
            }

            /* 响应式步骤指示器 */
            @media (max-width: 768px) {
                .step-indicator-container {
                    flex-direction: column;
                    align-items: center;
                }

                .step-indicator-line {
                    width: 2px;
                    height: 2rem;
                }
            }

            /* 必填字段标识 */
            .required-field::after {
                content: " *";
                color: #ef4444;
                font-weight: bold;
            }

            /* 错误字段样式 */
            .field-error {
                animation: shake 0.5s ease-in-out;
            }

            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }

            /* 步骤错误提示动画 */
            .step-error-message {
                animation: slideDown 0.3s ease-out;
            }

            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateY(-10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        }
    </style>
</head>
<body class="font-inter bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen flex flex-col">
<!-- 页面头部 -->
<header class="bg-white shadow-md py-6 px-4 md:px-8">
    <div class="container mx-auto flex justify-between items-center">
        <h1 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-neutral">
            <i class="fa fa-credit-card text-primary mr-2"></i>在线支付系统
        </h1>
        <p class="text-gray-500 hidden md:block">支持微信支付和支付宝支付</p>
    </div>
</header>

<!-- 主要内容区 -->
<main class="flex-grow container mx-auto px-4 py-8 md:py-16">
    <div class="max-w-4xl mx-auto">
        <!-- 支付方式选择 -->
        <div class="mb-8 text-center">
            <label class="block text-gray-700 mb-4 text-lg font-medium">选择支付方式:</label>
            <div class="flex justify-center gap-6 mb-6">
                <label class="flex items-center cursor-pointer">
                    <input type="radio" name="paymentMethod" value="wechat" checked class="sr-only">
                    <div class="payment-option w-32 h-16 border-2 border-green-500 bg-green-50 rounded-lg transition-all duration-300 hover:shadow-md p-2">
                        <img src="1.png" alt="微信支付" class="w-full h-full object-contain">
                    </div>
                </label>
                <label class="flex items-center cursor-pointer">
                    <input type="radio" name="paymentMethod" value="alipay" class="sr-only">
                    <div class="payment-option w-32 h-16 border-2 border-gray-300 bg-white rounded-lg transition-all duration-300 hover:shadow-md p-2">
                        <img src="2.png" alt="支付宝支付" class="w-full h-full object-contain">
                    </div>
                </label>
            </div>
        </div>

        <!-- 产品ID输入 -->
        <div class="mb-8 text-center">
            <label for="productId" class="block text-gray-700 mb-2">产品ID:</label>
            <input
                    type="text"
                    id="productId"
                    value="2"
                    class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent max-w-xs w-full"
            >
            <p class="text-gray-500 text-sm mt-2">请输入要获取二维码的产品ID</p>
        </div>

        <!-- 订单号展示区域 -->
        <div id="orderInfo" class="hidden mb-6 p-4 bg-blue-50 border border-blue-100 rounded-lg">
            <p class="text-gray-700"><span class="font-medium">订单号:</span> <span id="orderNo"></span></p>
        </div>

        <!-- 说明文字 -->
        <div class="text-center mb-10">
            <h2 id="paymentTitle" class="text-[clamp(1.2rem,2vw,1.8rem)] font-semibold text-neutral mb-3">微信支付下单</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">点击下方按钮创建订单，支付成功后可在订单操作中申请退款。</p>
        </div>

        <!-- 按钮区域 -->
        <div class="text-center mb-10">
            <button id="fetchQrCodeBtn" class="bg-primary hover:bg-primary/90 text-white font-medium py-3 px-8 rounded-full shadow-md btn-hover flex items-center justify-center mx-auto mb-6">
                <i class="fa fa-credit-card mr-2"></i>创建支付订单
            </button>
        </div>

        <!-- 商家入驻申请区域 -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-10">
            <div class="text-center mb-6">
                <h3 class="text-xl font-semibold text-gray-800">商家入驻申请</h3>
                <p class="text-gray-600 text-sm mt-2">填写商家信息申请成为微信支付特约商户</p>
            </div>

            <!-- 步骤指示器 -->
            <div class="flex justify-center mb-8">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <div id="step1-indicator" class="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center text-sm font-medium">1</div>
                        <span id="step1-text" class="ml-2 text-sm font-medium text-primary">超级管理员</span>
                    </div>
                    <div class="w-12 h-0.5 bg-gray-300"></div>
                    <div class="flex items-center">
                        <div id="step2-indicator" class="w-8 h-8 rounded-full bg-gray-300 text-gray-500 flex items-center justify-center text-sm font-medium">2</div>
                        <span id="step2-text" class="ml-2 text-sm font-medium text-gray-500">主体资料</span>
                    </div>
                    <div class="w-12 h-0.5 bg-gray-300"></div>
                    <div class="flex items-center">
                        <div id="step3-indicator" class="w-8 h-8 rounded-full bg-gray-300 text-gray-500 flex items-center justify-center text-sm font-medium">3</div>
                        <span id="step3-text" class="ml-2 text-sm font-medium text-gray-500">身份证件</span>
                    </div>
                    <div class="w-12 h-0.5 bg-gray-300"></div>
                    <div class="flex items-center">
                        <div id="step4-indicator" class="w-8 h-8 rounded-full bg-gray-300 text-gray-500 flex items-center justify-center text-sm font-medium">4</div>
                        <span id="step4-text" class="ml-2 text-sm font-medium text-gray-500">受益人信息</span>
                    </div>
                    <div class="w-12 h-0.5 bg-gray-300"></div>
                    <div class="flex items-center">
                        <div id="step5-indicator" class="w-8 h-8 rounded-full bg-gray-300 text-gray-500 flex items-center justify-center text-sm font-medium">5</div>
                        <span id="step5-text" class="ml-2 text-sm font-medium text-gray-500">经营资料</span>
                    </div>
                    <div class="w-12 h-0.5 bg-gray-300"></div>
                    <div class="flex items-center">
                        <div id="step6-indicator" class="w-8 h-8 rounded-full bg-gray-300 text-gray-500 flex items-center justify-center text-sm font-medium">6</div>
                        <span id="step6-text" class="ml-2 text-sm font-medium text-gray-500">银行账户</span>
                    </div>
                </div>
            </div>

            <!-- 入驻申请表单 -->
            <form id="merchantApplyForm" class="space-y-6">

                <!-- 第1步：超级管理员信息 -->

                <div id="step1" class="step-content">
                    <div class="border-t pt-6">
                    <h4 class="text-lg font-medium text-gray-800 mb-4">超级管理员信息</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="contactType" class="block text-sm font-medium text-gray-700 mb-2">超级管理员类型 *</label>
                            <select id="contactType" name="contactType" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                    onchange="toggleContactFields()">
                                <option value="">请选择</option>
                                <option value="LEGAL">经营者/法定代表人</option>
                                <option value="SUPER">经办人</option>
                            </select>
                        </div>
                        <div>
                            <label for="contactName" class="block text-sm font-medium text-gray-700 mb-2">超级管理员姓名 *</label>
                            <input type="text" id="contactName" name="contactName" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="请输入姓名">
                        </div>
                        <div>
                            <label for="mobilePhone" class="block text-sm font-medium text-gray-700 mb-2">联系手机 *</label>
                            <input type="tel" id="mobilePhone" name="mobilePhone" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="请输入手机号码">
                        </div>
                        <div>
                            <label for="contactEmail" class="block text-sm font-medium text-gray-700 mb-2">联系邮箱 *</label>
                            <input type="email" id="contactEmail" name="contactEmail" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="请输入邮箱地址">
                        </div>
                    </div>

                    <!-- 经办人额外信息 -->
                    <div id="contactExtraFields" class="hidden mt-6 p-4 bg-gray-50 rounded-lg">
                        <h5 class="text-md font-medium text-gray-800 mb-4">经办人证件信息</h5>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="contactIdDocType" class="block text-sm font-medium text-gray-700 mb-2">证件类型 *</label>
                                <select id="contactIdDocType" name="contactIdDocType"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value="">请选择</option>
                                    <option value="IDENTIFICATION_TYPE_IDCARD">中国大陆居民-身份证</option>
                                    <option value="IDENTIFICATION_TYPE_OVERSEA_PASSPORT">其他国家或地区居民-护照</option>
                                    <option value="IDENTIFICATION_TYPE_HONGKONG_PASSPORT">中国香港居民-来往内地通行证</option>
                                    <option value="IDENTIFICATION_TYPE_MACAO_PASSPORT">中国澳门居民-来往内地通行证</option>
                                    <option value="IDENTIFICATION_TYPE_TAIWAN_PASSPORT">中国台湾居民-来往大陆通行证</option>
                                    <option value="IDENTIFICATION_TYPE_FOREIGN_RESIDENT">外国人居留证</option>
                                    <option value="IDENTIFICATION_TYPE_HONGKONG_MACAO_RESIDENT">港澳居民证</option>
                                    <option value="IDENTIFICATION_TYPE_TAIWAN_RESIDENT">台湾居民证</option>
                                </select>
                            </div>
                            <div>
                                <label for="contactIdNumber" class="block text-sm font-medium text-gray-700 mb-2">证件号码 *</label>
                                <input type="text" id="contactIdNumber" name="contactIdNumber"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                       placeholder="请输入证件号码">
                            </div>
                            <div>
                                <label for="contactPeriodBegin" class="block text-sm font-medium text-gray-700 mb-2">证件有效期开始时间 *</label>
                                <input type="date" id="contactPeriodBegin" name="contactPeriodBegin"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                            <div>
                                <label for="contactPeriodEnd" class="block text-sm font-medium text-gray-700 mb-2">证件有效期结束时间 *</label>
                                <div class="space-y-2">
                                    <div class="flex items-center space-x-2">
                                        <input type="radio" id="contactPeriodEndDate" name="contactPeriodEndType" value="date" checked
                                               class="text-primary focus:ring-primary" onchange="toggleContactPeriodEnd()">
                                        <label for="contactPeriodEndDate" class="text-sm text-gray-700">具体日期</label>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <input type="radio" id="contactPeriodEndLongTerm" name="contactPeriodEndType" value="longterm"
                                               class="text-primary focus:ring-primary" onchange="toggleContactPeriodEnd()">
                                        <label for="contactPeriodEndLongTerm" class="text-sm text-gray-700">长期</label>
                                    </div>
                                    <input type="date" id="contactPeriodEnd" name="contactPeriodEnd"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>
                            </div>
                            <div>
                                <label for="contactIdDocCopy" class="block text-sm font-medium text-gray-700 mb-2">证件正面照片 *</label>
                                <div class="relative">
                                    <input type="file" id="contactIdDocCopy" name="contactIdDocCopy" accept="image/*"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <div id="contactIdDocCopyPreview" class="mt-2 hidden">
                                        <img id="contactIdDocCopyImg" src="" alt="证件正面预览" class="max-w-full h-32 object-contain border rounded">
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">请上传证件人像面照片，支持JPG、PNG格式</p>
                            </div>
                            <div>
                                <label for="contactIdDocCopyBack" class="block text-sm font-medium text-gray-700 mb-2">证件反面照片 *</label>
                                <div class="relative">
                                    <input type="file" id="contactIdDocCopyBack" name="contactIdDocCopyBack" accept="image/*"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <div id="contactIdDocCopyBackPreview" class="mt-2 hidden">
                                        <img id="contactIdDocCopyBackImg" src="" alt="证件反面预览" class="max-w-full h-32 object-contain border rounded">
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">请上传证件国徽面照片，支持JPG、PNG格式</p>
                            </div>
                            <div class="md:col-span-2">
                                <label for="businessAuthorizationLetter" class="block text-sm font-medium text-gray-700 mb-2">业务办理授权函 *</label>
                                <div class="relative">
                                    <input type="file" id="businessAuthorizationLetter" name="businessAuthorizationLetter" accept="image/*"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <div id="businessAuthorizationLetterPreview" class="mt-2 hidden">
                                        <img id="businessAuthorizationLetterImg" src="" alt="业务办理授权函预览" class="max-w-full h-32 object-contain border rounded">
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">请上传业务办理授权函，需加盖公章，支持JPG、PNG格式</p>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>

                <!-- 第2步：主体资料 -->
                <div id="step2" class="step-content hidden">
                    <!-- 主体资料 -->
                <div class="border-t pt-6">
                    <h4 class="text-lg font-medium text-gray-800 mb-4">主体资料</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="subjectType" class="block text-sm font-medium text-gray-700 mb-2">主体类型 *</label>
                            <select id="subjectType" name="subjectType" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                    onchange="toggleSubjectFields()">
                                <option value="">请选择</option>
                                <option value="SUBJECT_TYPE_INDIVIDUAL">个体户</option>
                                <option value="SUBJECT_TYPE_ENTERPRISE">企业</option>
                                <option value="SUBJECT_TYPE_GOVERNMENT">政府机关</option>
                                <option value="SUBJECT_TYPE_INSTITUTIONS">事业单位</option>
                                <option value="SUBJECT_TYPE_OTHERS">社会组织</option>
                            </select>
                        </div>
                        <div>
                            <label for="licenseCopy" class="block text-sm font-medium text-gray-700 mb-2">营业执照照片 *</label>
                            <div class="relative">
                                <input type="file" id="licenseCopy" name="licenseCopy" accept="image/*" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                <div id="licenseCopyPreview" class="mt-2 hidden">
                                    <img id="licenseCopyImg" src="" alt="营业执照预览" class="max-w-full h-32 object-contain border rounded">
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">请上传营业执照照片，支持JPG、PNG格式</p>
                        </div>
                        <div>
                            <label for="licenseNumber" class="block text-sm font-medium text-gray-700 mb-2">注册号/统一社会信用代码 *</label>
                            <input type="text" id="licenseNumber" name="licenseNumber" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="请输入营业执照上的注册号或统一社会信用代码">
                        </div>
                        <div>
                            <label for="merchantName" class="block text-sm font-medium text-gray-700 mb-2">商户名称 *</label>
                            <input type="text" id="merchantName" name="merchantName" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="请输入营业执照上的商户名称">
                        </div>
                        <div>
                            <label for="legalPerson" class="block text-sm font-medium text-gray-700 mb-2">
                                <span id="legalPersonLabel">个体户经营者/法定代表人姓名</span> *
                            </label>
                            <input type="text" id="legalPerson" name="legalPerson" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="请输入个体户经营者或法定代表人姓名">
                        </div>
                        <div>
                            <label for="registeredAddress" class="block text-sm font-medium text-gray-700 mb-2">注册地址 *</label>
                            <input type="text" id="registeredAddress" name="registeredAddress" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="请输入营业执照上的注册地址">
                        </div>
                        <div>
                            <label for="licensePeriodBegin" class="block text-sm font-medium text-gray-700 mb-2">有效期限开始日期 *</label>
                            <input type="date" id="licensePeriodBegin" name="licensePeriodBegin" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>
                        <div>
                            <label for="licensePeriodEnd" class="block text-sm font-medium text-gray-700 mb-2">有效期限结束日期 *</label>
                            <div class="space-y-2">
                                <div class="flex items-center space-x-2">
                                    <input type="radio" id="licensePeriodEndDate" name="licensePeriodEndType" value="date" checked
                                           class="text-primary focus:ring-primary" onchange="toggleLicensePeriodEnd()">
                                    <label for="licensePeriodEndDate" class="text-sm text-gray-700">具体日期</label>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <input type="radio" id="licensePeriodEndLongTerm" name="licensePeriodEndType" value="longterm"
                                           class="text-primary focus:ring-primary" onchange="toggleLicensePeriodEnd()">
                                    <label for="licensePeriodEndLongTerm" class="text-sm text-gray-700">长期</label>
                                </div>
                                <input type="date" id="licensePeriodEnd" name="licensePeriodEnd" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                        </div>
                    </div>
                </div>
                </div>

                <!-- 第3步：身份证件信息 -->
                <div id="step3" class="step-content hidden">
                    <!-- 身份证件信息 -->
                <div class="border-t pt-6">
                    <h4 class="text-lg font-medium text-gray-800 mb-4">身份证件信息</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="idCardName" class="block text-sm font-medium text-gray-700 mb-2">身份证姓名 *</label>
                            <input type="text" id="idCardName" name="idCardName" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="请输入身份证上的姓名">
                        </div>
                        <div>
                            <label for="idCardNumber" class="block text-sm font-medium text-gray-700 mb-2">身份证号码 *</label>
                            <input type="text" id="idCardNumber" name="idCardNumber" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="请输入身份证号码">
                        </div>
                        <div>
                            <label for="cardPeriodBegin" class="block text-sm font-medium text-gray-700 mb-2">身份证有效期开始时间 *</label>
                            <input type="date" id="cardPeriodBegin" name="cardPeriodBegin" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>
                        <div>
                            <label for="cardPeriodEnd" class="block text-sm font-medium text-gray-700 mb-2">身份证有效期结束时间 *</label>
                            <div class="space-y-2">
                                <div class="flex items-center space-x-2">
                                    <input type="radio" id="cardPeriodEndDate" name="cardPeriodEndType" value="date" checked
                                           class="text-primary focus:ring-primary" onchange="toggleCardPeriodEnd()">
                                    <label for="cardPeriodEndDate" class="text-sm text-gray-700">具体日期</label>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <input type="radio" id="cardPeriodEndLongTerm" name="cardPeriodEndType" value="longterm"
                                           class="text-primary focus:ring-primary" onchange="toggleCardPeriodEnd()">
                                    <label for="cardPeriodEndLongTerm" class="text-sm text-gray-700">长期</label>
                                </div>
                                <input type="date" id="cardPeriodEnd" name="cardPeriodEnd" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                        </div>
                        <div>
                            <label for="idCardCopy" class="block text-sm font-medium text-gray-700 mb-2">身份证人像面照片 *</label>
                            <div class="relative">
                                <input type="file" id="idCardCopy" name="idCardCopy" accept="image/*" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                <div id="idCardCopyPreview" class="mt-2 hidden">
                                    <img id="idCardCopyImg" src="" alt="身份证人像面预览" class="max-w-full h-32 object-contain border rounded">
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">请上传身份证人像面照片，支持JPG、PNG格式</p>
                        </div>
                        <div>
                            <label for="idCardNational" class="block text-sm font-medium text-gray-700 mb-2">身份证国徽面照片 *</label>
                            <div class="relative">
                                <input type="file" id="idCardNational" name="idCardNational" accept="image/*" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                <div id="idCardNationalPreview" class="mt-2 hidden">
                                    <img id="idCardNationalImg" src="" alt="身份证国徽面预览" class="max-w-full h-32 object-contain border rounded">
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">请上传身份证国徽面照片，支持JPG、PNG格式</p>
                        </div>

                        <!-- 企业类型时显示的居住地址 -->
                        <div id="idCardAddressField" class="hidden">
                            <label for="idCardAddress" class="block text-sm font-medium text-gray-700 mb-2">身份证居住地址 *</label>
                            <input type="text" id="idCardAddress" name="idCardAddress"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="请输入身份证上的居住地址">
                        </div>

                        <!-- 经办人时显示的法定代表人说明图片 -->
                        <div id="legalPersonProofField" class="hidden md:col-span-2">
                            <label for="legalPersonProof" class="block text-sm font-medium text-gray-700 mb-2">法定代表人说明图片 *</label>
                            <div class="relative">
                                <input type="file" id="legalPersonProof" name="legalPersonProof" accept="image/*"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                <div id="legalPersonProofPreview" class="mt-2 hidden">
                                    <img id="legalPersonProofImg" src="" alt="法定代表人说明图片预览" class="max-w-full h-32 object-contain border rounded">
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">当证件持有人类型为经办人时需要上传，支持JPG、PNG格式</p>
                        </div>
                    </div>
                </div>
                </div>

                <!-- 第4步：最终受益人信息 -->
                <div id="step4" class="step-content hidden">
                    <!-- 最终受益人信息 -->
                <div class="border-t pt-6">
                    <h4 class="text-lg font-medium text-gray-800 mb-4">最终受益人信息</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="beneficiaryIdDocType" class="block text-sm font-medium text-gray-700 mb-2">受益人证件类型 *</label>
                            <select id="beneficiaryIdDocType" name="beneficiaryIdDocType" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                <option value="">请选择</option>
                                <option value="IDENTIFICATION_TYPE_IDCARD">中国大陆居民-身份证</option>
                                <option value="IDENTIFICATION_TYPE_OVERSEA_PASSPORT">其他国家或地区居民-护照</option>
                                <option value="IDENTIFICATION_TYPE_HONGKONG_PASSPORT">中国香港居民-来往内地通行证</option>
                                <option value="IDENTIFICATION_TYPE_MACAO_PASSPORT">中国澳门居民-来往内地通行证</option>
                                <option value="IDENTIFICATION_TYPE_TAIWAN_PASSPORT">中国台湾居民-来往大陆通行证</option>
                                <option value="IDENTIFICATION_TYPE_FOREIGN_RESIDENT">外国人居留证</option>
                                <option value="IDENTIFICATION_TYPE_HONGKONG_MACAO_RESIDENT">港澳居民证</option>
                                <option value="IDENTIFICATION_TYPE_TAIWAN_RESIDENT">台湾居民证</option>
                            </select>
                        </div>
                        <div>
                            <label for="beneficiaryName" class="block text-sm font-medium text-gray-700 mb-2">受益人姓名 *</label>
                            <input type="text" id="beneficiaryName" name="beneficiaryName" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="请输入受益人姓名">
                        </div>
                        <div>
                            <label for="beneficiaryIdNumber" class="block text-sm font-medium text-gray-700 mb-2">受益人证件号码 *</label>
                            <input type="text" id="beneficiaryIdNumber" name="beneficiaryIdNumber" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="请输入受益人证件号码">
                        </div>
                        <div>
                            <label for="beneficiaryAddress" class="block text-sm font-medium text-gray-700 mb-2">受益人证件居住地址 *</label>
                            <input type="text" id="beneficiaryAddress" name="beneficiaryAddress" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="请输入受益人证件居住地址">
                        </div>
                        <div>
                            <label for="beneficiaryPeriodBegin" class="block text-sm font-medium text-gray-700 mb-2">受益人证件有效期开始时间 *</label>
                            <input type="date" id="beneficiaryPeriodBegin" name="beneficiaryPeriodBegin" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>
                        <div>
                            <label for="beneficiaryPeriodEnd" class="block text-sm font-medium text-gray-700 mb-2">受益人证件有效期结束时间 *</label>
                            <div class="space-y-2">
                                <div class="flex items-center space-x-2">
                                    <input type="radio" id="beneficiaryPeriodEndDate" name="beneficiaryPeriodEndType" value="date" checked
                                           class="text-primary focus:ring-primary" onchange="toggleBeneficiaryPeriodEnd()">
                                    <label for="beneficiaryPeriodEndDate" class="text-sm text-gray-700">具体日期</label>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <input type="radio" id="beneficiaryPeriodEndLongTerm" name="beneficiaryPeriodEndType" value="longterm"
                                           class="text-primary focus:ring-primary" onchange="toggleBeneficiaryPeriodEnd()">
                                    <label for="beneficiaryPeriodEndLongTerm" class="text-sm text-gray-700">长期</label>
                                </div>
                                <input type="date" id="beneficiaryPeriodEnd" name="beneficiaryPeriodEnd" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                        </div>
                        <div>
                            <label for="beneficiaryIdDocCopy" class="block text-sm font-medium text-gray-700 mb-2">受益人证件正面照片 *</label>
                            <div class="relative">
                                <input type="file" id="beneficiaryIdDocCopy" name="beneficiaryIdDocCopy" accept="image/*" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                <div id="beneficiaryIdDocCopyPreview" class="mt-2 hidden">
                                    <img id="beneficiaryIdDocCopyImg" src="" alt="受益人证件正面预览" class="max-w-full h-32 object-contain border rounded">
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">请上传受益人证件正面照片，支持JPG、PNG格式</p>
                        </div>
                        <div>
                            <label for="beneficiaryIdDocCopyBack" class="block text-sm font-medium text-gray-700 mb-2">受益人证件反面照片 *</label>
                            <div class="relative">
                                <input type="file" id="beneficiaryIdDocCopyBack" name="beneficiaryIdDocCopyBack" accept="image/*" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                <div id="beneficiaryIdDocCopyBackPreview" class="mt-2 hidden">
                                    <img id="beneficiaryIdDocCopyBackImg" src="" alt="受益人证件反面预览" class="max-w-full h-32 object-contain border rounded">
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">请上传受益人证件反面照片，支持JPG、PNG格式</p>
                        </div>
                    </div>
                </div>
                </div>

                <!-- 第5步：经营资料 -->
                <div id="step5" class="step-content hidden">
                    <!-- 经营资料 -->
                <div class="border-t pt-6">
                    <h4 class="text-lg font-medium text-gray-800 mb-4">经营资料</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="merchantShortname" class="block text-sm font-medium text-gray-700 mb-2">商户简称 *</label>
                            <input type="text" id="merchantShortname" name="merchantShortname" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="在支付完成页向买家展示的简称">
                            <p class="text-xs text-gray-500 mt-1">1-64个字符，需与微信经营类目相关</p>
                        </div>
                        <div>
                            <label for="servicePhone" class="block text-sm font-medium text-gray-700 mb-2">客服电话 *</label>
                            <input type="tel" id="servicePhone" name="servicePhone" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="将在交易记录中向买家展示">
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">经营场景类型 *</label>
                            <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                                <label class="flex items-center">
                                    <input type="checkbox" name="salesScenesType" value="SALES_SCENES_STORE"
                                           class="rounded border-gray-300 text-primary focus:ring-primary">
                                    <span class="ml-2 text-sm">线下场所</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="salesScenesType" value="SALES_SCENES_MP"
                                           class="rounded border-gray-300 text-primary focus:ring-primary">
                                    <span class="ml-2 text-sm">服务号与公众号</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="salesScenesType" value="SALES_SCENES_MINI_PROGRAM"
                                           class="rounded border-gray-300 text-primary focus:ring-primary">
                                    <span class="ml-2 text-sm">小程序</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="salesScenesType" value="SALES_SCENES_WEB"
                                           class="rounded border-gray-300 text-primary focus:ring-primary">
                                    <span class="ml-2 text-sm">互联网网站</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="salesScenesType" value="SALES_SCENES_APP"
                                           class="rounded border-gray-300 text-primary focus:ring-primary">
                                    <span class="ml-2 text-sm">App</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="salesScenesType" value="SALES_SCENES_WEWORK"
                                           class="rounded border-gray-300 text-primary focus:ring-primary">
                                    <span class="ml-2 text-sm">企业微信</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                </div>

                <!-- 第6步：结算银行账户 -->
                <div id="step6" class="step-content hidden">
                    <!-- 结算银行账户 -->
                <div class="border-t pt-6">
                    <h4 class="text-lg font-medium text-gray-800 mb-4">结算银行账户</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="bankAccountType" class="block text-sm font-medium text-gray-700 mb-2">账户类型 *</label>
                            <select id="bankAccountType" name="bankAccountType" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                <option value="">请选择</option>
                                <option value="BANK_ACCOUNT_TYPE_CORPORATE">对公银行账户</option>
                                <option value="BANK_ACCOUNT_TYPE_PERSONAL">经营者个人银行卡</option>
                            </select>
                        </div>
                        <div>
                            <label for="accountName" class="block text-sm font-medium text-gray-700 mb-2">开户名称 *</label>
                            <input type="text" id="accountName" name="accountName" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="请输入开户名称">
                        </div>
                        <div>
                            <label for="accountBank" class="block text-sm font-medium text-gray-700 mb-2">开户银行 *</label>
                            <select id="accountBank" name="accountBank" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                <option value="">请选择银行</option>
                                <option value="工商银行">工商银行</option>
                                <option value="建设银行">建设银行</option>
                                <option value="农业银行">农业银行</option>
                                <option value="中国银行">中国银行</option>
                                <option value="交通银行">交通银行</option>
                                <option value="招商银行">招商银行</option>
                                <option value="浦发银行">浦发银行</option>
                                <option value="民生银行">民生银行</option>
                                <option value="兴业银行">兴业银行</option>
                                <option value="平安银行">平安银行</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div>
                            <label for="accountNumber" class="block text-sm font-medium text-gray-700 mb-2">银行账号 *</label>
                            <input type="text" id="accountNumber" name="accountNumber" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="请输入银行账号">
                        </div>
                    </div>
                </div>
                </div>

                <!-- 导航按钮 -->
                <div class="flex justify-between items-center pt-6 border-t mt-6">
                    <button type="button" id="prevBtn" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-6 rounded-lg shadow-md btn-hover hidden">
                        <i class="fa fa-arrow-left mr-2"></i>上一步
                    </button>
                    <div class="flex space-x-4">
                        <button type="button" id="nextBtn" class="bg-primary hover:bg-primary/90 text-white font-medium py-2 px-6 rounded-lg shadow-md btn-hover">
                            下一步<i class="fa fa-arrow-right ml-2"></i>
                        </button>
                        <button type="submit" id="submitMerchantApply" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-6 rounded-lg shadow-md btn-hover hidden">
                            <i class="fa fa-paper-plane mr-2"></i>提交申请
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- 账单下载区域 -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-10">
            <div class="text-center mb-6">
                <h3 class="text-xl font-semibold text-gray-800">账单下载</h3>
                <p class="text-gray-600 text-sm mt-2">选择日期下载交易账单或资金账单</p>
            </div>

            <!-- 日期选择 -->
            <div class="mb-6 text-center">
                <label for="billDate" class="block text-gray-700 mb-2">账单日期:</label>
                <input
                        type="date"
                        id="billDate"
                        class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent max-w-xs w-full"
                >
                <p class="text-gray-500 text-sm mt-2">请选择要下载账单的日期（仅限前一天及之前）</p>
            </div>

            <!-- 下载按钮 -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <button id="downloadTradeBillBtn" class="bg-secondary hover:bg-secondary/90 text-white font-medium py-3 px-6 rounded-lg shadow-md btn-hover flex items-center justify-center min-w-[160px]">
                    <i class="fa fa-download mr-2"></i>下载交易账单
                </button>
                <button id="downloadFundBillBtn" class="bg-orange-500 hover:bg-orange-600 text-white font-medium py-3 px-6 rounded-lg shadow-md btn-hover flex items-center justify-center min-w-[160px]">
                    <i class="fa fa-download mr-2"></i>下载资金账单
                </button>
            </div>
        </div>

        <!-- 加载状态 -->
        <div id="loadingIndicator" class="hidden text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            <p class="mt-4 text-gray-600">正在创建订单，请稍候...</p>
        </div>

        <!-- 错误信息 -->
        <div id="errorMessage" class="hidden bg-red-50 border border-red-200 text-red-700 px-6 py-4 rounded-lg mb-8 text-center">
            <i class="fa fa-exclamation-circle mr-2"></i>
            <span id="errorText">操作失败，请稍后重试</span>
        </div>

        <!-- 成功信息 -->
        <div id="successMessage" class="hidden bg-green-50 border border-green-200 text-green-700 px-6 py-4 rounded-lg mb-8 text-center">
            <i class="fa fa-check-circle mr-2"></i>
            <span id="successText">操作成功</span>
        </div>

        <!-- 订单状态展示区域 - 表格样式 -->
        <div id="orderStatusContainer" class="bg-white rounded-xl shadow-md p-6">
            <div class="text-center mb-6">
                <h3 class="text-xl font-semibold text-gray-800">订单信息</h3>
            </div>
            <div>
                <table class="w-full table-auto">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-4 py-3 text-left text-sm font-medium text-gray-600 rounded-l-lg">订单号</th>
                            <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">商品ID</th>
                            <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">订单状态</th>
                            <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">支付方式</th>
                            <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">创建时间</th>
                            <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">支付金额</th>
                            <th class="px-4 py-3 text-left text-sm font-medium text-gray-600 rounded-r-lg">操作</th>
                        </tr>
                    </thead>
                    <tbody id="orderTableBody">
                        <!-- 订单数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</main>

<!-- 二维码弹窗 -->
<div id="qrcodeModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-xl shadow-2xl p-6 max-w-md w-full mx-4 relative">
        <!-- 关闭按钮 -->
        <button id="closeModalBtn" class="absolute top-4 right-4 text-gray-400 hover:text-red-500 text-2xl transition-colors" title="取消订单">
            <i class="fa fa-times"></i>
        </button>

        <!-- 弹窗标题 -->
        <div class="text-center mb-6">
            <h3 id="modalTitle" class="text-xl font-semibold text-gray-800">微信支付二维码</h3>
            <p id="modalDescription" class="text-gray-600 text-sm mt-2">请使用微信扫描下方二维码完成支付</p>
        </div>

        <!-- 二维码展示区域 -->
        <div id="modalQrcodeContainer" class="qrcode-container min-h-[280px] p-4 mb-4">
            <div id="modalInitialState" class="text-center p-8 text-gray-500">
                <i class="fa fa-qrcode text-4xl mb-4 opacity-30"></i>
                <p>二维码加载中...</p>
            </div>
            <!-- 二维码将被渲染到这个canvas元素 -->
            <canvas id="modalQrcodeCanvas" class="hidden max-w-full h-auto mx-auto"></canvas>
            <!-- 备用img元素，用于显示生成的二维码图片 -->
            <img id="modalQrcodeImage" src="" alt="" class="hidden max-w-full h-auto mx-auto">
        </div>

        <!-- 二维码链接信息 -->
        <div id="modalQrcodeInfo" class="hidden p-3 bg-gray-50 rounded-lg text-xs text-gray-600 break-all">
            <p><span class="font-medium">二维码链接:</span> <span id="modalCodeUrlText"></span></p>
        </div>

        <!-- 支付状态提示 -->
        <div class="text-center mt-4">
            <p class="text-sm text-gray-500">支付完成后页面将自动更新状态</p>
            <p class="text-xs text-gray-400 mt-1">点击弹窗外部区域可取消订单</p>
        </div>
    </div>
</div>

<!-- 页脚 -->
<footer class="bg-neutral text-white py-6 px-4">
    <div class="container mx-auto text-center text-gray-300 text-sm">
        <p>在线支付系统 &copy; 2025</p>
    </div>
</footer>

<script>
    // RSA加密配置
    const RSA_CONFIG = {
        // 微信支付公钥（从后端动态获取）
        publicKey: null,
        publicKeyId: null,
        // 需要加密的敏感字段列表
        sensitiveFields: [
            'contactName', 'mobilePhone', 'contactEmail', 'idCardName', 'idCardNumber',
            'legalPerson', 'beneficiaryName', 'beneficiaryIdNumber', 'beneficiaryAddress',
            'accountName', 'accountNumber', 'agentIdNumber', 'agentName'
        ]
    };

    // RSA加密工具类
    class RSAEncryption {
        constructor() {
            this.encrypt = new JSEncrypt();
            this.initialized = false;
        }

        // 从后端获取公钥并初始化
        async initialize() {
            if (this.initialized) {
                return true;
            }

            try {
                const response = await fetch('http://127.0.0.1:8081/api/wx-pay/public-key', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`获取公钥失败，状态码: ${response.status}`);
                }

                const data = await response.json();

                if (data.code !== 200) {
                    throw new Error(data.msg || '获取公钥失败');
                }

                // 设置公钥
                RSA_CONFIG.publicKey = data.data.publicKey;
                RSA_CONFIG.publicKeyId = data.data.publicKeyId;
                this.encrypt.setPublicKey(RSA_CONFIG.publicKey);
                this.initialized = true;

                console.log('RSA公钥初始化成功，公钥ID:', RSA_CONFIG.publicKeyId);
                return true;

            } catch (error) {
                console.error('获取公钥失败:', error);
                return false;
            }
        }

        // 加密单个字符串
        async encryptString(plainText) {
            if (!plainText || typeof plainText !== 'string') {
                return plainText;
            }

            // 确保已初始化
            if (!this.initialized) {
                const success = await this.initialize();
                if (!success) {
                    console.warn('RSA未初始化，跳过加密');
                    return plainText;
                }
            }

            try {
                // 检查字符串长度（RSA 2048位密钥最大加密长度约214字节）
                const utf8Length = new TextEncoder().encode(plainText).length;
                if (utf8Length > 214) {
                    console.warn(`字符串长度超过RSA加密限制: ${utf8Length} 字节`);
                    return plainText; // 超长字符串不加密
                }

                const encrypted = this.encrypt.encrypt(plainText);
                if (!encrypted) {
                    console.error('RSA加密失败');
                    return plainText;
                }

                console.log(`已加密字段，原文长度: ${utf8Length} 字节`);
                return encrypted;
            } catch (error) {
                console.error('RSA加密异常:', error);
                return plainText;
            }
        }

        // 批量加密对象中的敏感字段
        async encryptSensitiveData(data) {
            if (!data || typeof data !== 'object') {
                return data;
            }

            // 确保已初始化
            if (!this.initialized) {
                const success = await this.initialize();
                if (!success) {
                    console.warn('RSA未初始化，跳过加密');
                    return data;
                }
            }

            const encryptedData = { ...data };

            // 使用 Promise.all 并行加密所有敏感字段
            const encryptPromises = RSA_CONFIG.sensitiveFields.map(async field => {
                if (encryptedData[field]) {
                    const originalValue = encryptedData[field];
                    const encryptedValue = await this.encryptString(originalValue);
                    if (encryptedValue !== originalValue) {
                        encryptedData[field] = encryptedValue;
                        console.log(`已加密字段: ${field}`);
                    }
                }
            });

            await Promise.all(encryptPromises);
            return encryptedData;
        }
    }

    // 创建全局RSA加密实例
    const rsaEncryption = new RSAEncryption();

    // 获取DOM元素
    const fetchQrCodeBtn = document.getElementById('fetchQrCodeBtn');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const errorMessage = document.getElementById('errorMessage');
    const errorText = document.getElementById('errorText');
    const successMessage = document.getElementById('successMessage');
    const successText = document.getElementById('successText');
    const productIdInput = document.getElementById('productId');
    const orderInfo = document.getElementById('orderInfo');
    const orderNoElement = document.getElementById('orderNo');

    // 支付方式相关元素
    const paymentMethodRadios = document.querySelectorAll('input[name="paymentMethod"]');
    const paymentTitle = document.getElementById('paymentTitle');
    const modalTitle = document.getElementById('modalTitle');
    const modalDescription = document.getElementById('modalDescription');

    // 账单下载相关元素
    const billDateInput = document.getElementById('billDate');
    const downloadTradeBillBtn = document.getElementById('downloadTradeBillBtn');
    const downloadFundBillBtn = document.getElementById('downloadFundBillBtn');

    // 商家入驻申请相关元素
    const merchantApplyForm = document.getElementById('merchantApplyForm');
    const submitMerchantApplyBtn = document.getElementById('submitMerchantApply');

    // 订单状态相关元素
    const orderStatusContainer = document.getElementById('orderStatusContainer');
    const orderTableBody = document.getElementById('orderTableBody');

    // 弹窗相关元素
    const qrcodeModal = document.getElementById('qrcodeModal');
    const closeModalBtn = document.getElementById('closeModalBtn');
    const modalQrcodeContainer = document.getElementById('modalQrcodeContainer');
    const modalQrcodeCanvas = document.getElementById('modalQrcodeCanvas');
    const modalQrcodeImage = document.getElementById('modalQrcodeImage');
    const modalQrcodeInfo = document.getElementById('modalQrcodeInfo');
    const modalCodeUrlText = document.getElementById('modalCodeUrlText');
    const modalInitialState = document.getElementById('modalInitialState');

    // 全局变量
    let currentOrderNo = null;
    let orderStatusTimer = null;
    let currentPaymentMethod = 'wechat'; // 默认微信支付

    // 点击按钮事件
    fetchQrCodeBtn.addEventListener('click', fetchAndDisplayQrCode);
    closeModalBtn.addEventListener('click', cancelOrderAndCloseModal);

    // 账单下载按钮事件
    downloadTradeBillBtn.addEventListener('click', () => downloadBill('tradebill'));
    downloadFundBillBtn.addEventListener('click', () => downloadBill('fundflowbill'));

    // 商家入驻申请表单提交事件
    merchantApplyForm.addEventListener('submit', handleMerchantApplySubmit);

    // 步骤导航相关元素
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    let currentStep = 1;
    const totalSteps = 6;

    // 导航按钮事件
    prevBtn.addEventListener('click', () => {
        console.log('点击上一步，当前步骤:', currentStep);
        if (currentStep > 1) {
            currentStep--;
            showStep(currentStep);
        }
    });

    nextBtn.addEventListener('click', () => {
        console.log('点击下一步，当前步骤:', currentStep);
        if (validateCurrentStep()) {
            if (currentStep < totalSteps) {
                currentStep++;
                showStep(currentStep);
            }
        }
    });

    // 初始化显示第一步
    console.log('初始化分页表单');
    console.log('prevBtn:', prevBtn);
    console.log('nextBtn:', nextBtn);
    console.log('submitBtn:', submitMerchantApplyBtn);

    showStep(1);

    // 身份证照片预览功能
    document.getElementById('idCardCopy').addEventListener('change', function(e) {
        previewImage(e.target, 'idCardCopyPreview', 'idCardCopyImg');
    });

    document.getElementById('idCardNational').addEventListener('change', function(e) {
        previewImage(e.target, 'idCardNationalPreview', 'idCardNationalImg');
    });

    // 经办人证件照片预览功能
    document.getElementById('contactIdDocCopy').addEventListener('change', function(e) {
        previewImage(e.target, 'contactIdDocCopyPreview', 'contactIdDocCopyImg');
    });

    document.getElementById('contactIdDocCopyBack').addEventListener('change', function(e) {
        previewImage(e.target, 'contactIdDocCopyBackPreview', 'contactIdDocCopyBackImg');
    });

    document.getElementById('businessAuthorizationLetter').addEventListener('change', function(e) {
        previewImage(e.target, 'businessAuthorizationLetterPreview', 'businessAuthorizationLetterImg');
    });

    // 营业执照照片预览
    document.getElementById('licenseCopy').addEventListener('change', function(e) {
        previewImage(e.target, 'licenseCopyPreview', 'licenseCopyImg');
    });

    // 法定代表人说明图片预览
    document.getElementById('legalPersonProof').addEventListener('change', function(e) {
        previewImage(e.target, 'legalPersonProofPreview', 'legalPersonProofImg');
    });

    // 受益人证件照片预览
    document.getElementById('beneficiaryIdDocCopy').addEventListener('change', function(e) {
        previewImage(e.target, 'beneficiaryIdDocCopyPreview', 'beneficiaryIdDocCopyImg');
    });

    document.getElementById('beneficiaryIdDocCopyBack').addEventListener('change', function(e) {
        previewImage(e.target, 'beneficiaryIdDocCopyBackPreview', 'beneficiaryIdDocCopyBackImg');
    });

    // 添加实时验证
    addRealTimeValidation();

    // 支付方式选择事件
    paymentMethodRadios.forEach(radio => {
        radio.addEventListener('change', handlePaymentMethodChange);
    });

    // 页面加载时查询订单列表和设置默认日期
    window.addEventListener('load', function() {
        loadOrderList();
        setDefaultBillDate();
        initializePaymentMethodSelection();
    });

    // 点击弹窗背景取消订单
    qrcodeModal.addEventListener('click', function(e) {
        if (e.target === qrcodeModal) {
            cancelOrderAndCloseModal();
        }
    });

    // 表格中的退款按钮事件（延迟绑定，因为按钮可能还未创建）
    document.addEventListener('click', function(e) {
        if (e.target && e.target.classList.contains('refund-btn')) {
            const orderNo = e.target.getAttribute('data-order-no');
            handleRefund(orderNo);
        }
        // 取消订单按钮事件
        if (e.target && e.target.classList.contains('cancel-btn')) {
            const orderNo = e.target.getAttribute('data-order-no');
            handleCancelOrder(orderNo);
        }
    });

    // 加载订单列表
    async function loadOrderList() {
        try {
            const response = await fetch('http://127.0.0.1:8081/api/order-info/list', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`请求失败，状态码: ${response.status}`);
            }

            const data = await response.json();

            if (data.code !== 200) {
                throw new Error(data.msg || '获取订单列表失败');
            }

            // 渲染订单列表
            renderOrderList(data.data || []);

        } catch (error) {
            console.error('加载订单列表失败:', error);
            // 显示空状态
            renderEmptyState();
        }
    }

    // 渲染订单列表
    function renderOrderList(orders) {
        if (!orders || orders.length === 0) {
            renderEmptyState();
            return;
        }

        orderTableBody.innerHTML = '';

        orders.forEach(order => {
            const row = createOrderRow(order);
            orderTableBody.appendChild(row);
        });

        // 如果有订单，设置第一个订单为当前订单（用于二维码显示）
        if (orders.length > 0) {
            const firstOrder = orders[0];
            currentOrderNo = firstOrder.orderNo;
            if (firstOrder.codeUrl) {
                window.currentQrCodeUrl = firstOrder.codeUrl;
            }
        }
    }

    // 渲染空状态
    function renderEmptyState() {
        orderTableBody.innerHTML = `
            <tr class="border-t border-gray-100">
                <td class="px-4 py-4 text-sm text-gray-800 font-mono">暂无订单</td>
                <td class="px-4 py-4 text-sm text-gray-600">-</td>
                <td class="px-4 py-4 text-sm">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                        暂无数据
                    </span>
                </td>
                <td class="px-4 py-4 text-sm text-gray-600">-</td>
                <td class="px-4 py-4 text-sm text-gray-600">-</td>
                <td class="px-4 py-4 text-sm font-medium text-gray-600">-</td>
                <td class="px-4 py-4 text-sm">
                    <div class="flex gap-2 flex-nowrap">
                        <button class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-gray-400 bg-gray-100 cursor-not-allowed whitespace-nowrap" disabled>
                            <i class="fa fa-undo mr-1"></i>退款
                        </button>
                        <button class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-gray-400 bg-gray-100 cursor-not-allowed whitespace-nowrap" disabled>
                            <i class="fa fa-times mr-1"></i>取消
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    // 创建订单行
    function createOrderRow(order) {
        const row = document.createElement('tr');
        row.className = 'border-t border-gray-100';

        // 格式化订单状态
        const statusInfo = getOrderStatusInfo(order.orderStatus);

        // 格式化金额
        const amount = order.totalFee ? `¥${(order.totalFee / 100).toFixed(2)}` : '-';

        // 格式化时间
        const createTime = order.createTime ? new Date(order.createTime).toLocaleString() : '-';

        // 格式化支付方式
        const paymentType = order.paymentType || '-';

        row.innerHTML = `
            <td class="px-4 py-4 text-sm text-gray-800 font-mono">${order.orderNo || '-'}</td>
            <td class="px-4 py-4 text-sm text-gray-600">${order.productId || '-'}</td>
            <td class="px-4 py-4 text-sm">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusInfo.className}">
                    ${statusInfo.text}
                </span>
            </td>
            <td class="px-4 py-4 text-sm text-gray-600">${paymentType}</td>
            <td class="px-4 py-4 text-sm text-gray-600">${createTime}</td>
            <td class="px-4 py-4 text-sm font-medium text-green-600">${amount}</td>
            <td class="px-4 py-4 text-sm">
                <div class="flex gap-2 flex-nowrap">
                    <button class="refund-btn inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded ${statusInfo.buttonClass} whitespace-nowrap"
                            data-order-no="${order.orderNo}"
                            ${statusInfo.buttonDisabled ? 'disabled' : ''}>
                        <i class="fa fa-undo mr-1"></i>退款
                    </button>
                    <button class="cancel-btn inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded ${statusInfo.cancelButtonClass} whitespace-nowrap"
                            data-order-no="${order.orderNo}"
                            ${statusInfo.cancelButtonDisabled ? 'disabled' : ''}>
                        <i class="fa fa-times mr-1"></i>取消
                    </button>
                </div>
            </td>
        `;

        return row;
    }

    // 获取订单状态信息
    function getOrderStatusInfo(orderStatus) {
        switch (orderStatus) {
            case '支付成功':
                return {
                    text: '支付成功',
                    className: 'bg-green-100 text-green-800',
                    buttonClass: 'text-white bg-red-500 hover:bg-red-600 transition-colors',
                    buttonDisabled: false,
                    cancelButtonClass: 'text-gray-400 bg-gray-100 cursor-not-allowed',
                    cancelButtonDisabled: true
                };
            case '未支付':
                return {
                    text: '未支付',
                    className: 'bg-blue-100 text-blue-800',
                    buttonClass: 'text-gray-400 bg-gray-100 cursor-not-allowed',
                    buttonDisabled: true,
                    cancelButtonClass: 'text-white bg-orange-500 hover:bg-orange-600 transition-colors',
                    cancelButtonDisabled: false
                };
            case '用户已取消':
                return {
                    text: '已取消',
                    className: 'bg-red-100 text-red-800',
                    buttonClass: 'text-gray-400 bg-gray-100 cursor-not-allowed',
                    buttonDisabled: true,
                    cancelButtonClass: 'text-gray-400 bg-gray-100 cursor-not-allowed',
                    cancelButtonDisabled: true
                };
            case '退款中':
                return {
                    text: '退款处理中',
                    className: 'bg-orange-100 text-orange-800',
                    buttonClass: 'text-gray-400 bg-gray-100 cursor-not-allowed',
                    buttonDisabled: true,
                    cancelButtonClass: 'text-gray-400 bg-gray-100 cursor-not-allowed',
                    cancelButtonDisabled: true
                };
            case '已退款':
                return {
                    text: '已退款',
                    className: 'bg-purple-100 text-purple-800',
                    buttonClass: 'text-gray-400 bg-gray-100 cursor-not-allowed',
                    buttonDisabled: true,
                    cancelButtonClass: 'text-gray-400 bg-gray-100 cursor-not-allowed',
                    cancelButtonDisabled: true
                };
            case '退款异常':
                return {
                    text: '退款异常',
                    className: 'bg-red-100 text-red-800',
                    buttonClass: 'text-gray-400 bg-gray-100 cursor-not-allowed',
                    buttonDisabled: true,
                    cancelButtonClass: 'text-gray-400 bg-gray-100 cursor-not-allowed',
                    cancelButtonDisabled: true
                };
            case '超时已关闭':
                return {
                    text: '已关闭',
                    className: 'bg-gray-100 text-gray-800',
                    buttonClass: 'text-gray-400 bg-gray-100 cursor-not-allowed',
                    buttonDisabled: true,
                    cancelButtonClass: 'text-gray-400 bg-gray-100 cursor-not-allowed',
                    cancelButtonDisabled: true
                };
            default:
                return {
                    text: orderStatus || '未知状态',
                    className: 'bg-gray-100 text-gray-800',
                    buttonClass: 'text-gray-400 bg-gray-100 cursor-not-allowed',
                    buttonDisabled: true,
                    cancelButtonClass: 'text-gray-400 bg-gray-100 cursor-not-allowed',
                    cancelButtonDisabled: true
                };
        }
    }

    // 发送POST请求获取二维码链接并生成展示
    async function fetchAndDisplayQrCode() {
        // 获取用户输入的productId
        const productId = productIdInput.value.trim();

        // 验证productId
        if (!productId) {
            showError('请输入产品ID');
            return;
        }

        // 重置状态
        errorMessage.classList.add('hidden');
        successMessage.classList.add('hidden');
        orderInfo.classList.add('hidden');

        // 显示加载状态
        loadingIndicator.classList.remove('hidden');
        fetchQrCodeBtn.disabled = true;
        fetchQrCodeBtn.classList.add('opacity-70', 'cursor-not-allowed');

        try {
            // 根据支付方式选择API地址
            let apiUrl;
            let method = 'POST';

            if (currentPaymentMethod === 'wechat') {
                apiUrl = `http://127.0.0.1:8081/api/wx-pay/native/${productId}`;
            } else {
                apiUrl = `http://127.0.0.1:8081/api/ali-pay/trade/page/pay/${productId}`;
            }

            // 发送请求到后端API
            const response = await fetch(apiUrl, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    // 可以根据需要添加其他请求头，如认证信息
                    // 'Authorization': 'Bearer your-token-here'
                }
            });

            // 检查响应状态
            if (!response.ok) {
                throw new Error(`请求失败，状态码: ${response.status}`);
            }

            // 解析JSON响应
            const data = await response.json();

            // 检查响应状态码
            if (data.code !== 200) {
                throw new Error(data.msg || '操作失败');
            }

            // 处理不同支付方式的响应
            if (currentPaymentMethod === 'wechat') {
                // 微信支付：检查响应中是否包含二维码URL
                if (!data.data || !data.data.codeUrl) {
                    throw new Error('响应中未包含二维码链接');
                }

                const codeUrl = data.data.codeUrl;
                const orderNo = data.data.orderNo;

                // 保存二维码URL到全局变量
                window.currentQrCodeUrl = codeUrl;

                // 处理微信支付订单信息
                await handleWechatPaymentSuccess(data, orderNo);

            } else {
                // 支付宝支付：直接跳转到支付页面
                if (!data.data) {
                    throw new Error('支付宝支付响应异常');
                }

                // 支付宝返回的是HTML表单，直接在新窗口中打开
                const newWindow = window.open('', '_blank');
                newWindow.document.write(data.data);
                newWindow.document.close();

                // 显示成功信息
                successText.textContent = '支付宝支付页面已打开，请在新窗口中完成支付';
                successMessage.classList.remove('hidden');

                // 隐藏加载状态
                loadingIndicator.classList.add('hidden');
                return; // 支付宝支付不需要显示二维码弹窗
            }

            // 隐藏加载状态
            loadingIndicator.classList.add('hidden');

            // 立即显示二维码弹窗（仅微信支付）
            if (currentPaymentMethod === 'wechat') {
                await showQrCodeModal();
            }

        } catch (error) {
            console.error('创建订单失败:', error);
            loadingIndicator.classList.add('hidden');
            showError(error.message || '创建订单失败，请稍后重试');
        } finally {
            // 恢复按钮状态
            fetchQrCodeBtn.disabled = false;
            fetchQrCodeBtn.classList.remove('opacity-70', 'cursor-not-allowed');
        }
    }

    // 生成二维码 - 在弹窗中显示
    function generateQrCodeInModal(text) {
        return new Promise((resolve, reject) => {
            try {
                // 隐藏初始状态
                modalInitialState.classList.add('hidden');

                // 确保canvas元素存在且干净
                const canvas = modalQrcodeCanvas;
                if (!canvas) {
                    reject(new Error('未找到二维码画布元素'));
                    return;
                }

                // 清除之前的内容
                const ctx = canvas.getContext('2d');
                if (ctx) {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                }

                // 显示canvas
                canvas.classList.remove('hidden');

                // 配置二维码选项
                const options = {
                    errorCorrectionLevel: 'H', // 高容错率
                    width: 250, // 固定宽度适合弹窗
                    margin: 2,
                    color: {
                        dark: '#000000ff',
                        light: '#ffffffff'
                    }
                };

                // 尝试使用toCanvas方法生成二维码
                QRCode.toCanvas(canvas, text, options, (error) => {
                    if (error) {
                        console.error('Canvas生成二维码失败:', error);
                        // 如果canvas方法失败，尝试使用图片方法作为备选
                        generateQrCodeAsImageInModal(text, resolve, reject);
                        return;
                    }

                    // 添加淡入动画
                    canvas.style.opacity = '0';
                    canvas.style.transition = 'opacity 0.5s ease-in-out';
                    setTimeout(() => {
                        canvas.style.opacity = '1';
                        resolve();
                    }, 50);
                });
            } catch (error) {
                console.error('生成二维码过程出错:', error);
                reject(new Error(`生成二维码失败: ${error.message}`));
            }
        });
    }

    // 备选方案：生成二维码图片（弹窗版本）
    function generateQrCodeAsImageInModal(text, resolve, reject) {
        try {
            const img = modalQrcodeImage;
            if (!img) {
                reject(new Error('未找到二维码图片元素'));
                return;
            }

            // 隐藏canvas，显示img
            modalQrcodeCanvas.classList.add('hidden');
            img.classList.remove('hidden');

            QRCode.toDataURL(text, {
                errorCorrectionLevel: 'H',
                width: 250,
                margin: 2
            }, (error, url) => {
                if (error) {
                    reject(new Error(`生成二维码图片失败: ${error.message}`));
                    return;
                }

                img.src = url;
                img.alt = '支付二维码';

                // 添加加载完成事件
                img.onload = () => {
                    img.style.opacity = '0';
                    img.style.transition = 'opacity 0.5s ease-in-out';
                    setTimeout(() => {
                        img.style.opacity = '1';
                        resolve();
                    }, 50);
                };

                img.onerror = () => {
                    reject(new Error('二维码图片加载失败'));
                };
            });
        } catch (error) {
            reject(new Error(`图片备选方案失败: ${error.message}`));
        }
    }



    // 重置弹窗二维码显示
    function resetModalQrCodeDisplay() {
        // 显示初始状态
        modalInitialState.classList.remove('hidden');

        // 清除canvas内容
        if (modalQrcodeCanvas) {
            const ctx = modalQrcodeCanvas.getContext('2d');
            if (ctx) {
                ctx.clearRect(0, 0, modalQrcodeCanvas.width, modalQrcodeCanvas.height);
            }
            modalQrcodeCanvas.classList.add('hidden');
        }

        // 清除图片
        if (modalQrcodeImage) {
            modalQrcodeImage.src = '';
            modalQrcodeImage.classList.add('hidden');
        }

        // 隐藏二维码信息
        modalQrcodeInfo.classList.add('hidden');
    }

    // 显示错误信息
    function showError(message) {
        errorText.textContent = message;
        errorMessage.classList.remove('hidden');
    }

    // 初始化支付方式选择
    function initializePaymentMethodSelection() {
        // 设置默认选中状态的样式
        updatePaymentMethodUI();
    }

    // 处理支付方式变化
    function handlePaymentMethodChange(event) {
        currentPaymentMethod = event.target.value;
        updatePaymentMethodUI();
        updatePageContent();
    }

    // 更新支付方式UI
    function updatePaymentMethodUI() {
        const paymentOptions = document.querySelectorAll('.payment-option');
        paymentOptions.forEach((option, index) => {
            const radio = paymentMethodRadios[index];
            if (radio.checked) {
                option.classList.add('selected');
                if (radio.value === 'wechat') {
                    option.classList.remove('border-gray-300', 'bg-white');
                    option.classList.add('border-green-500', 'bg-green-50');
                } else {
                    option.classList.remove('border-gray-300', 'bg-white');
                    option.classList.add('border-blue-500', 'bg-blue-50');
                }
            } else {
                option.classList.remove('selected', 'border-green-500', 'bg-green-50', 'border-blue-500', 'bg-blue-50');
                option.classList.add('border-gray-300', 'bg-white');
            }
        });
    }

    // 更新页面内容
    function updatePageContent() {
        if (currentPaymentMethod === 'wechat') {
            paymentTitle.textContent = '微信支付下单';
            modalTitle.textContent = '微信支付二维码';
            modalDescription.textContent = '请使用微信扫描下方二维码完成支付';
        } else {
            paymentTitle.textContent = '支付宝支付下单';
            modalTitle.textContent = '支付宝支付二维码';
            modalDescription.textContent = '请使用支付宝扫描下方二维码完成支付';
        }
    }

    // 处理微信支付成功响应
    async function handleWechatPaymentSuccess(data, orderNo) {
        // 显示成功信息
        successText.textContent = data.msg || '操作成功';
        successMessage.classList.remove('hidden');

        // 保存当前订单号
        currentOrderNo = orderNo;

        // 显示订单号
        if (orderNo) {
            orderNoElement.textContent = orderNo;
            orderInfo.classList.remove('hidden');

            // 重新加载订单列表
            await loadOrderList();

            // 开始定时查询订单状态
            startOrderStatusPolling(orderNo);
        }
    }



    // 显示二维码弹窗
    async function showQrCodeModal() {
        if (!window.currentQrCodeUrl) {
            showError('二维码链接不存在，请重新获取');
            return;
        }

        // 重置弹窗显示
        resetModalQrCodeDisplay();

        // 显示弹窗
        qrcodeModal.classList.remove('hidden');

        try {
            // 生成二维码
            await generateQrCodeInModal(window.currentQrCodeUrl);

            // 显示二维码链接信息
            modalCodeUrlText.textContent = window.currentQrCodeUrl;
            modalQrcodeInfo.classList.remove('hidden');
        } catch (error) {
            console.error('生成二维码失败:', error);
            modalInitialState.innerHTML = `
                <i class="fa fa-exclamation-triangle text-4xl mb-4 text-red-400"></i>
                <p class="text-red-600">二维码生成失败</p>
            `;
        }
    }

    // 关闭二维码弹窗
    function closeQrCodeModal() {
        qrcodeModal.classList.add('hidden');
        resetModalQrCodeDisplay();
    }

    // 取消订单并关闭弹窗
    async function cancelOrderAndCloseModal() {
        if (!currentOrderNo) {
            closeQrCodeModal();
            return;
        }

        // 显示确认对话框
        if (!confirm('确定要取消订单吗？取消后将无法继续支付。')) {
            return;
        }

        try {
            // 显示加载状态
            modalInitialState.classList.remove('hidden');
            modalInitialState.innerHTML = `
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                <p class="mt-2 text-gray-600">正在取消订单...</p>
            `;

            // 调用取消订单API
            const response = await fetch(`http://127.0.0.1:8081/api/wx-pay/cancel/${currentOrderNo}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`请求失败，状态码: ${response.status}`);
            }

            const data = await response.json();

            if (data.code !== 200) {
                throw new Error(data.msg || '取消订单失败');
            }

            // 重新加载订单列表
            await loadOrderList();

            // 停止订单状态轮询
            if (orderStatusTimer) {
                clearInterval(orderStatusTimer);
                orderStatusTimer = null;
            }

            // 显示成功消息
            successText.textContent = '订单已取消';
            successMessage.classList.remove('hidden');

            // 关闭弹窗
            closeQrCodeModal();

        } catch (error) {
            console.error('取消订单失败:', error);

            // 显示错误信息
            modalInitialState.innerHTML = `
                <i class="fa fa-exclamation-triangle text-4xl mb-4 text-red-400"></i>
                <p class="text-red-600">取消订单失败</p>
                <p class="text-sm text-gray-500 mt-2">${error.message}</p>
            `;

            // 3秒后恢复二维码显示
            setTimeout(() => {
                if (window.currentQrCodeUrl) {
                    generateQrCodeInModal(window.currentQrCodeUrl);
                }
            }, 3000);
        }
    }

    // 开始订单状态轮询
    function startOrderStatusPolling(orderNo) {
        // 清除之前的定时器
        if (orderStatusTimer) {
            clearInterval(orderStatusTimer);
        }

        // 每3秒查询一次订单状态
        orderStatusTimer = setInterval(async () => {
            try {
                await checkOrderStatus(orderNo);
            } catch (error) {
                console.error('查询订单状态失败:', error);
            }
        }, 3000);
    }

    // 查询订单状态
    async function checkOrderStatus(orderNo) {
        try {
            const response = await fetch(`http://127.0.0.1:8081/api/order-info/query-order-status/${orderNo}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`请求失败，状态码: ${response.status}`);
            }

            const data = await response.json();

            if (data.code === 200) {
                // 支付成功，重新加载订单列表
                await loadOrderList();

                // 停止轮询
                if (orderStatusTimer) {
                    clearInterval(orderStatusTimer);
                    orderStatusTimer = null;
                }

                // 显示成功消息
                successText.textContent = '支付成功！';
                successMessage.classList.remove('hidden');

                // 自动关闭弹窗
                if (!qrcodeModal.classList.contains('hidden')) {
                    setTimeout(() => {
                        closeQrCodeModal();
                    }, 2000);
                }

            } else if (data.code === 204) {
                // 支付中，继续轮询（不需要更新显示，保持当前状态）
            }

        } catch (error) {
            console.error('查询订单状态失败:', error);
            // 不显示错误，继续轮询
        }
    }

    // 处理退款
    async function handleRefund(orderNo) {
        if (!orderNo) {
            showError('没有可退款的订单');
            return;
        }

        // 显示确认对话框
        const reason = prompt('请输入退款原因（必填）：', '用户申请退款');
        if (!reason || reason.trim() === '') {
            return; // 用户取消或未输入退款原因
        }

        try {
            // 显示加载状态
            const refundBtn = document.querySelector(`[data-order-no="${orderNo}"]`);
            const originalText = refundBtn.innerHTML;
            refundBtn.innerHTML = '<i class="fa fa-spinner fa-spin mr-1"></i>退款中...';
            refundBtn.disabled = true;

            // 调用统一退款API
            const response = await fetch(`http://127.0.0.1:8081/api/order-info/refund/${orderNo}/${encodeURIComponent(reason.trim())}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`请求失败，状态码: ${response.status}`);
            }

            const data = await response.json();

            if (data.code !== 200) {
                throw new Error(data.msg || '退款申请失败');
            }

            // 退款申请成功
            successText.textContent = data.msg || '退款申请成功，请等待处理';
            successMessage.classList.remove('hidden');

            // 重新加载订单列表
            await loadOrderList();

            // 停止订单状态轮询
            if (orderStatusTimer) {
                clearInterval(orderStatusTimer);
                orderStatusTimer = null;
            }

        } catch (error) {
            console.error('退款申请失败:', error);
            showError(error.message || '退款申请失败，请稍后重试');

            // 恢复按钮状态
            const refundBtn = document.querySelector(`[data-order-no="${orderNo}"]`);
            if (refundBtn) {
                refundBtn.innerHTML = '<i class="fa fa-undo mr-1"></i>申请退款';
                refundBtn.disabled = false;
            }
        }
    }

    // 处理取消订单
    async function handleCancelOrder(orderNo) {
        if (!orderNo) {
            showError('没有可取消的订单');
            return;
        }

        // 显示确认对话框
        if (!confirm('确定要取消订单吗？取消后将无法继续支付。')) {
            return;
        }

        try {
            // 显示加载状态
            const cancelBtn = document.querySelector(`[data-order-no="${orderNo}"].cancel-btn`);
            const originalText = cancelBtn.innerHTML;
            cancelBtn.innerHTML = '<i class="fa fa-spinner fa-spin mr-1"></i>取消中...';
            cancelBtn.disabled = true;

            // 调用取消订单API - 根据支付方式选择不同的接口
            let apiUrl;
            // 这里可以根据订单的支付方式来选择不同的取消接口
            // 先尝试微信支付的取消接口，如果失败再尝试支付宝的
            try {
                apiUrl = `http://127.0.0.1:8081/api/wx-pay/cancel/${orderNo}`;
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`请求失败，状态码: ${response.status}`);
                }

                const data = await response.json();

                if (data.code !== 200) {
                    // 如果微信支付取消失败，尝试支付宝取消接口
                    throw new Error(data.msg || '取消订单失败');
                }

                // 取消成功
                successText.textContent = data.msg || '订单已取消';
                successMessage.classList.remove('hidden');

            } catch (wxError) {
                // 尝试支付宝取消接口
                try {
                    apiUrl = `http://127.0.0.1:8081/api/ali-pay/trade/close/${orderNo}`;
                    const response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`请求失败，状态码: ${response.status}`);
                    }

                    const data = await response.json();

                    if (data.code !== 200) {
                        throw new Error(data.msg || '取消订单失败');
                    }

                    // 取消成功
                    successText.textContent = data.msg || '订单已取消';
                    successMessage.classList.remove('hidden');

                } catch (aliError) {
                    throw new Error('取消订单失败：' + (wxError.message || aliError.message));
                }
            }

            // 重新加载订单列表
            await loadOrderList();

            // 停止订单状态轮询
            if (orderStatusTimer) {
                clearInterval(orderStatusTimer);
                orderStatusTimer = null;
            }

        } catch (error) {
            console.error('取消订单失败:', error);
            showError(error.message || '取消订单失败，请稍后重试');

            // 恢复按钮状态
            const cancelBtn = document.querySelector(`[data-order-no="${orderNo}"].cancel-btn`);
            if (cancelBtn) {
                cancelBtn.innerHTML = '<i class="fa fa-times mr-1"></i>取消';
                cancelBtn.disabled = false;
            }
        }
    }

    // 设置默认账单日期为前一天，并限制最大可选日期
    function setDefaultBillDate() {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1); // 设置为前一天
        const year = yesterday.getFullYear();
        const month = String(yesterday.getMonth() + 1).padStart(2, '0');
        const day = String(yesterday.getDate()).padStart(2, '0');
        const yesterdayString = `${year}-${month}-${day}`;

        // 设置默认值为前一天
        billDateInput.value = yesterdayString;

        // 设置最大可选日期为前一天
        billDateInput.setAttribute('max', yesterdayString);
    }

    // 下载账单函数
    async function downloadBill(billType) {
        // 获取选择的日期
        const billDate = billDateInput.value.trim();

        // 验证日期
        if (!billDate) {
            showError('请选择账单日期');
            return;
        }

        // 验证日期格式 (YYYY-MM-DD)
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(billDate)) {
            showError('请选择正确的日期格式');
            return;
        }

        // 确定按钮和账单类型名称
        const isTradeType = billType === 'tradebill';
        const btnElement = isTradeType ? downloadTradeBillBtn : downloadFundBillBtn;
        const billTypeName = isTradeType ? '交易账单' : '资金账单';

        try {
            // 重置状态
            errorMessage.classList.add('hidden');
            successMessage.classList.add('hidden');

            // 显示加载状态
            const originalText = btnElement.innerHTML;
            btnElement.innerHTML = '<i class="fa fa-spinner fa-spin mr-2"></i>下载中...';
            btnElement.disabled = true;
            btnElement.classList.add('opacity-70', 'cursor-not-allowed');

            // 调用下载API
            const apiUrl = `http://127.0.0.1:8081/api/wx-pay/downloadbill/${billDate}/${billType}`;

            const response = await fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            // 检查响应状态
            if (!response.ok) {
                throw new Error(`请求失败，状态码: ${response.status}`);
            }

            // 解析JSON响应
            const data = await response.json();

            // 检查响应状态码
            if (data.code !== 200) {
                throw new Error(data.msg || `下载${billTypeName}失败`);
            }

            // 创建下载文件
            const billContent = data.data || '';
            const blob = new Blob([billContent], { type: 'text/csv;charset=utf-8' });
            const url = window.URL.createObjectURL(blob);

            // 创建下载链接
            const link = document.createElement('a');
            link.href = url;
            link.download = `${billTypeName}_${billDate}.csv`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 释放URL对象
            window.URL.revokeObjectURL(url);

            // 显示成功信息
            successText.textContent = `${billTypeName}下载成功`;
            successMessage.classList.remove('hidden');

        } catch (error) {
            console.error(`下载${billTypeName}失败:`, error);
            showError(error.message || `下载${billTypeName}失败，请稍后重试`);
        } finally {
            // 恢复按钮状态
            btnElement.innerHTML = isTradeType ?
                '<i class="fa fa-download mr-2"></i>下载交易账单' :
                '<i class="fa fa-download mr-2"></i>下载资金账单';
            btnElement.disabled = false;
            btnElement.classList.remove('opacity-70', 'cursor-not-allowed');
        }
    }

    // 处理商家入驻申请表单提交
    async function handleMerchantApplySubmit(event) {
        event.preventDefault();

        // 获取表单数据
        const formData = new FormData(merchantApplyForm);

        // 验证必填字段
        const requiredFields = [
            'contactType', 'contactName', 'mobilePhone', 'contactEmail',
            'subjectType', 'licenseNumber', 'merchantName', 'legalPerson', 'registeredAddress', 'licensePeriodBegin',
            'idCardName', 'idCardNumber', 'cardPeriodBegin',
            'beneficiaryIdDocType', 'beneficiaryName', 'beneficiaryIdNumber', 'beneficiaryAddress', 'beneficiaryPeriodBegin',
            'merchantShortname', 'servicePhone',
            'bankAccountType', 'accountName', 'accountBank', 'accountNumber'
        ];

        for (const field of requiredFields) {
            if (!formData.get(field)) {
                showError(`请填写${getFieldLabel(field)}`);
                return;
            }
        }

        // 验证主体类型相关字段
        const subjectType = formData.get('subjectType');
        if (subjectType === 'SUBJECT_TYPE_ENTERPRISE') {
            if (!formData.get('idCardAddress')) {
                showError('企业类型需要填写身份证居住地址');
                return;
            }
        }

        // 验证各种有效期结束时间
        const cardPeriodEnd = getCardPeriodEnd();
        if (!cardPeriodEnd) {
            showError('请选择身份证有效期结束时间');
            return;
        }

        const licensePeriodEnd = getLicensePeriodEnd();
        if (!licensePeriodEnd) {
            showError('请选择营业执照有效期结束时间');
            return;
        }

        const beneficiaryPeriodEnd = getBeneficiaryPeriodEnd();
        if (!beneficiaryPeriodEnd) {
            showError('请选择受益人证件有效期结束时间');
            return;
        }

        // 验证超级管理员类型相关字段
        const contactType = formData.get('contactType');
        if (contactType === 'SUPER') {
            // 验证经办人额外字段
            const contactExtraFields = [
                'contactIdDocType', 'contactIdNumber', 'contactPeriodBegin'
            ];

            for (const field of contactExtraFields) {
                if (!formData.get(field)) {
                    showError(`请填写${getFieldLabel(field)}`);
                    return;
                }
            }

            // 验证经办人证件有效期结束时间
            const contactPeriodEnd = getContactPeriodEnd();
            if (!contactPeriodEnd) {
                showError('请选择经办人证件有效期结束时间');
                return;
            }

            // 验证经办人证件照片是否已上传到微信服务器
            const contactIdDocCopyMediaId = document.getElementById('contactIdDocCopy').dataset.mediaId;
            const contactIdDocCopyBackMediaId = document.getElementById('contactIdDocCopyBack').dataset.mediaId;
            const businessAuthorizationLetterMediaId = document.getElementById('businessAuthorizationLetter').dataset.mediaId;

            if (!contactIdDocCopyMediaId) {
                showError('请上传经办人证件正面照片并等待上传完成');
                return;
            }

            if (!contactIdDocCopyBackMediaId) {
                showError('请上传经办人证件反面照片并等待上传完成');
                return;
            }

            if (!businessAuthorizationLetterMediaId) {
                showError('请上传业务办理授权函并等待上传完成');
                return;
            }
        }

        // 验证各种图片是否已上传到微信服务器
        const licenseCopyMediaId = document.getElementById('licenseCopy').dataset.mediaId;
        if (!licenseCopyMediaId) {
            showError('请上传营业执照照片并等待上传完成');
            return;
        }

        const idCardCopyMediaId = document.getElementById('idCardCopy').dataset.mediaId;
        const idCardNationalMediaId = document.getElementById('idCardNational').dataset.mediaId;

        if (!idCardCopyMediaId) {
            showError('请上传身份证人像面照片并等待上传完成');
            return;
        }

        if (!idCardNationalMediaId) {
            showError('请上传身份证国徽面照片并等待上传完成');
            return;
        }

        // 验证法定代表人说明图片（企业 + 经办人时需要）
        if (contactType === 'SUPER' && subjectType === 'SUBJECT_TYPE_ENTERPRISE') {
            const legalPersonProofMediaId = document.getElementById('legalPersonProof').dataset.mediaId;
            if (!legalPersonProofMediaId) {
                showError('请上传法定代表人说明图片并等待上传完成');
                return;
            }
        }

        // 验证受益人证件照片
        const beneficiaryIdDocCopyMediaId = document.getElementById('beneficiaryIdDocCopy').dataset.mediaId;
        const beneficiaryIdDocCopyBackMediaId = document.getElementById('beneficiaryIdDocCopyBack').dataset.mediaId;

        if (!beneficiaryIdDocCopyMediaId) {
            showError('请上传受益人证件正面照片并等待上传完成');
            return;
        }

        if (!beneficiaryIdDocCopyBackMediaId) {
            showError('请上传受益人证件反面照片并等待上传完成');
            return;
        }

        // 验证经营场景类型至少选择一个
        const salesScenesTypes = formData.getAll('salesScenesType');
        if (salesScenesTypes.length === 0) {
            showError('请至少选择一个经营场景类型');
            return;
        }

        try {
            // 显示加载状态
            submitMerchantApplyBtn.disabled = true;
            submitMerchantApplyBtn.innerHTML = '<i class="fa fa-spinner fa-spin mr-2"></i>提交中...';
            submitMerchantApplyBtn.classList.add('opacity-70', 'cursor-not-allowed');

            // 构建请求数据
            const requestData = buildMerchantApplyData(formData, salesScenesTypes);

            // 对敏感数据进行RSA加密
            console.log('开始对敏感数据进行RSA加密...');
            const encryptedData = await rsaEncryption.encryptSensitiveData(requestData);

            // 调试信息
            console.log('发送商家入驻申请请求（已加密敏感数据）:', encryptedData);

            // 发送请求到后端API
            const headers = {
                'Content-Type': 'application/json'
            };

            // 如果RSA加密已初始化，添加公钥ID头
            if (RSA_CONFIG.publicKeyId) {
                headers['Wechatpay-Serial'] = RSA_CONFIG.publicKeyId;
                console.log('添加公钥ID头:', RSA_CONFIG.publicKeyId);
            }

            const response = await fetch('http://127.0.0.1:8081/api/wx-pay/applyment', {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(encryptedData)
            });

            console.log('收到响应:', response);
            console.log('响应状态:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('响应错误内容:', errorText);
                throw new Error(`请求失败，状态码: ${response.status}, 错误信息: ${errorText}`);
            }

            const data = await response.json();
            console.log('响应数据:', data);

            if (data.code !== 200) {
                throw new Error(data.msg || '申请提交失败');
            }

            // 显示成功信息
            successText.textContent = data.msg || '商家入驻申请提交成功，请等待审核';
            successMessage.classList.remove('hidden');

            // 重置表单
            merchantApplyForm.reset();

        } catch (error) {
            console.error('商家入驻申请失败:', error);

            // 检查是否是网络连接问题
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                showError('网络连接失败，请检查后端服务是否启动（http://127.0.0.1:8081）');
            } else if (error.message.includes('状态码')) {
                showError(`服务器错误：${error.message}`);
            } else {
                showError(error.message || '申请提交失败，请稍后重试');
            }
        } finally {
            // 恢复按钮状态
            submitMerchantApplyBtn.disabled = false;
            submitMerchantApplyBtn.innerHTML = '<i class="fa fa-paper-plane mr-2"></i>提交入驻申请';
            submitMerchantApplyBtn.classList.remove('opacity-70', 'cursor-not-allowed');
        }
    }

    // 构建商家入驻申请数据
    function buildMerchantApplyData(formData, salesScenesTypes) {
        const contactType = formData.get('contactType');

        // 基础数据
        const requestData = {
            // 超级管理员信息
            contact_type: contactType,
            contact_name: formData.get('contactName'),
            mobile_phone: formData.get('mobilePhone'),
            contact_email: formData.get('contactEmail'),

            // 主体资料
            subject_type: formData.get('subjectType'),
            license_copy: document.getElementById('licenseCopy').dataset.mediaId || '',
            license_number: formData.get('licenseNumber'),
            merchant_name: formData.get('merchantName'),
            legal_person: formData.get('legalPerson'),
            registered_address: formData.get('registeredAddress'),
            license_period_begin: formData.get('licensePeriodBegin'),
            license_period_end: getLicensePeriodEnd(),

            // 身份证件信息
            id_doc_type: 'IDENTIFICATION_TYPE_IDCARD', // 固定为身份证类型
            id_card_copy: document.getElementById('idCardCopy').dataset.mediaId || '',
            id_card_national: document.getElementById('idCardNational').dataset.mediaId || '',
            id_card_name: formData.get('idCardName'),
            id_card_number: formData.get('idCardNumber'),
            card_period_begin: formData.get('cardPeriodBegin'),
            card_period_end: getCardPeriodEnd(),

            // 最终受益人信息
            beneficiary_id_doc_type: formData.get('beneficiaryIdDocType'),
            beneficiary_name: formData.get('beneficiaryName'),
            beneficiary_id_number: formData.get('beneficiaryIdNumber'),
            beneficiary_address: formData.get('beneficiaryAddress'),
            beneficiary_period_begin: formData.get('beneficiaryPeriodBegin'),
            beneficiary_period_end: getBeneficiaryPeriodEnd(),
            beneficiary_id_doc_copy: document.getElementById('beneficiaryIdDocCopy').dataset.mediaId || '',
            beneficiary_id_doc_copy_back: document.getElementById('beneficiaryIdDocCopyBack').dataset.mediaId || '',

            // 经营资料
            merchant_shortname: formData.get('merchantShortname'),
            service_phone: formData.get('servicePhone'),
            sales_scenes_type: salesScenesTypes.join(','), // 转换为字符串

            // 银行账户信息
            bank_account_type: formData.get('bankAccountType'),
            account_name: formData.get('accountName'),
            account_bank: formData.get('accountBank'),
            account_number: formData.get('accountNumber'),

            // 其他可能需要的字段
            web_authorisation: 'temp_web_auth', // 临时值
            bank_branch_id: 'temp_branch_id', // 临时值
            bank_name: formData.get('accountBank') // 使用银行名称
        };

        // 企业类型时添加身份证居住地址
        if (formData.get('subjectType') === 'SUBJECT_TYPE_ENTERPRISE') {
            requestData.id_card_address = formData.get('idCardAddress');
        }

        // 企业 + 经办人时添加法定代表人说明图片
        if (contactType === 'SUPER' && formData.get('subjectType') === 'SUBJECT_TYPE_ENTERPRISE') {
            requestData.legal_person_proof = document.getElementById('legalPersonProof').dataset.mediaId || '';
        }

        // 如果是经办人，添加额外的证件信息
        if (contactType === 'SUPER') {
            requestData.contact_id_doc_type = formData.get('contactIdDocType');
            requestData.contact_id_number = formData.get('contactIdNumber');
            requestData.contact_id_doc_copy = document.getElementById('contactIdDocCopy').dataset.mediaId || '';
            requestData.contact_id_doc_copy_back = document.getElementById('contactIdDocCopyBack').dataset.mediaId || '';
            requestData.contact_period_begin = formData.get('contactPeriodBegin');
            requestData.contact_period_end = getContactPeriodEnd();
            requestData.business_authorization_letter = document.getElementById('businessAuthorizationLetter').dataset.mediaId || '';
        }

        return requestData;
    }

    // 获取字段标签
    function getFieldLabel(fieldName) {
        const labels = {
            'contactType': '超级管理员类型',
            'contactName': '超级管理员姓名',
            'mobilePhone': '联系手机',
            'contactEmail': '联系邮箱',
            'contactIdDocType': '经办人证件类型',
            'contactIdNumber': '经办人证件号码',
            'contactPeriodBegin': '经办人证件有效期开始时间',
            'contactPeriodEnd': '经办人证件有效期结束时间',
            'subjectType': '主体类型',
            'licenseNumber': '注册号/统一社会信用代码',
            'merchantName': '商户名称',
            'legalPerson': '个体户经营者/法定代表人姓名',
            'registeredAddress': '注册地址',
            'licensePeriodBegin': '营业执照有效期开始日期',
            'idCardName': '身份证姓名',
            'idCardNumber': '身份证号码',
            'cardPeriodBegin': '身份证有效期开始时间',
            'cardPeriodEnd': '身份证有效期结束时间',
            'idCardAddress': '身份证居住地址',
            'beneficiaryIdDocType': '受益人证件类型',
            'beneficiaryName': '受益人姓名',
            'beneficiaryIdNumber': '受益人证件号码',
            'beneficiaryAddress': '受益人证件居住地址',
            'beneficiaryPeriodBegin': '受益人证件有效期开始时间',
            'merchantShortname': '商户简称',
            'servicePhone': '客服电话',
            'bankAccountType': '账户类型',
            'accountName': '开户名称',
            'accountBank': '开户银行',
            'accountNumber': '银行账号'
        };
        return labels[fieldName] || fieldName;
    }

    // 上传图片到微信服务器获取MediaID
    async function uploadImageToWechat(file) {
        const formData = new FormData();
        formData.append('file', file);

        try {
            const response = await fetch('http://127.0.0.1:8081/api/wx-pay/media-upload', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`上传失败，状态码: ${response.status}`);
            }

            const data = await response.json();

            if (data.code !== 200) {
                throw new Error(data.msg || '图片上传失败');
            }

            return data.data; // 返回MediaID
        } catch (error) {
            console.error('图片上传失败:', error);
            throw error;
        }
    }

    // 图片预览功能（增强版，支持上传到微信服务器）
    async function previewImage(input, previewId, imgId) {
        const file = input.files[0];
        const preview = document.getElementById(previewId);
        const img = document.getElementById(imgId);

        if (file) {
            // 验证文件类型
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
            if (!allowedTypes.includes(file.type)) {
                showError('请上传JPG或PNG格式的图片');
                input.value = '';
                preview.classList.add('hidden');
                return;
            }

            // 验证文件大小（限制为5MB）
            if (file.size > 5 * 1024 * 1024) {
                showError('图片大小不能超过5MB');
                input.value = '';
                preview.classList.add('hidden');
                return;
            }

            // 显示预览
            const reader = new FileReader();
            reader.onload = function(e) {
                img.src = e.target.result;
                preview.classList.remove('hidden');
            };
            reader.readAsDataURL(file);

            // 上传到微信服务器获取MediaID
            try {
                // 显示上传状态
                const uploadStatus = document.createElement('div');
                uploadStatus.className = 'text-xs text-blue-600 mt-1';
                uploadStatus.textContent = '正在上传到微信服务器...';
                preview.appendChild(uploadStatus);

                const mediaId = await uploadImageToWechat(file);

                // 保存MediaID到input的data属性
                input.dataset.mediaId = mediaId;

                // 更新状态
                uploadStatus.textContent = '上传成功 ✓';
                uploadStatus.className = 'text-xs text-green-600 mt-1';

            } catch (error) {
                // 显示错误状态
                const uploadStatus = document.createElement('div');
                uploadStatus.className = 'text-xs text-red-600 mt-1';
                uploadStatus.textContent = '上传失败，请重新选择';
                preview.appendChild(uploadStatus);

                // 清空MediaID
                delete input.dataset.mediaId;

                showError('图片上传到微信服务器失败：' + error.message);
            }
        } else {
            preview.classList.add('hidden');
            // 清空MediaID
            delete input.dataset.mediaId;
        }
    }

    // 切换超级管理员类型时显示/隐藏额外字段
    function toggleContactFields() {
        const contactType = document.getElementById('contactType').value;
        const extraFields = document.getElementById('contactExtraFields');
        const requiredFields = [
            'contactIdDocType', 'contactIdNumber', 'contactPeriodBegin', 'contactPeriodEnd',
            'contactIdDocCopy', 'contactIdDocCopyBack', 'businessAuthorizationLetter'
        ];

        if (contactType === 'SUPER') {
            // 显示经办人额外字段
            extraFields.classList.remove('hidden');
            // 设置为必填
            requiredFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.required = true;
                }
            });
            // 检查是否需要显示法定代表人说明图片
            toggleSubjectFields();
        } else {
            // 隐藏经办人额外字段
            extraFields.classList.add('hidden');
            // 取消必填
            requiredFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.required = false;
                    field.value = ''; // 清空值
                }
            });
            // 重置经办人证件有效期选项
            const contactDateRadio = document.getElementById('contactPeriodEndDate');
            const contactLongTermRadio = document.getElementById('contactPeriodEndLongTerm');
            const contactDateInput = document.getElementById('contactPeriodEnd');
            if (contactDateRadio) {
                contactDateRadio.checked = true;
                contactDateInput.style.display = 'block';
                contactDateInput.required = false;
                contactDateInput.value = '';
            }
            // 清空图片预览
            ['contactIdDocCopyPreview', 'contactIdDocCopyBackPreview', 'businessAuthorizationLetterPreview'].forEach(previewId => {
                const preview = document.getElementById(previewId);
                if (preview) {
                    preview.classList.add('hidden');
                }
            });
        }
    }

    // 切换身份证有效期类型
    function toggleCardPeriodEnd() {
        const dateRadio = document.getElementById('cardPeriodEndDate');
        const dateInput = document.getElementById('cardPeriodEnd');

        if (dateRadio.checked) {
            // 选择具体日期
            dateInput.style.display = 'block';
            dateInput.required = true;
            dateInput.value = '';
        } else {
            // 选择长期
            dateInput.style.display = 'none';
            dateInput.required = false;
            dateInput.value = '长期';
        }
    }

    // 切换经办人证件有效期类型
    function toggleContactPeriodEnd() {
        const dateRadio = document.getElementById('contactPeriodEndDate');
        const dateInput = document.getElementById('contactPeriodEnd');

        if (dateRadio.checked) {
            // 选择具体日期
            dateInput.style.display = 'block';
            dateInput.required = true;
            dateInput.value = '';
        } else {
            // 选择长期
            dateInput.style.display = 'none';
            dateInput.required = false;
            dateInput.value = '长期';
        }
    }

    // 获取身份证有效期结束时间
    function getCardPeriodEnd() {
        const longTermRadio = document.getElementById('cardPeriodEndLongTerm');
        if (longTermRadio.checked) {
            return '长期';
        } else {
            return document.getElementById('cardPeriodEnd').value;
        }
    }

    // 获取经办人证件有效期结束时间
    function getContactPeriodEnd() {
        const longTermRadio = document.getElementById('contactPeriodEndLongTerm');
        if (longTermRadio.checked) {
            return '长期';
        } else {
            return document.getElementById('contactPeriodEnd').value;
        }
    }

    // 切换营业执照有效期类型
    function toggleLicensePeriodEnd() {
        const dateRadio = document.getElementById('licensePeriodEndDate');
        const dateInput = document.getElementById('licensePeriodEnd');

        if (dateRadio.checked) {
            // 选择具体日期
            dateInput.style.display = 'block';
            dateInput.required = true;
            dateInput.value = '';
        } else {
            // 选择长期
            dateInput.style.display = 'none';
            dateInput.required = false;
            dateInput.value = '长期';
        }
    }

    // 获取营业执照有效期结束时间
    function getLicensePeriodEnd() {
        const longTermRadio = document.getElementById('licensePeriodEndLongTerm');
        if (longTermRadio.checked) {
            return '长期';
        } else {
            return document.getElementById('licensePeriodEnd').value;
        }
    }

    // 切换受益人证件有效期类型
    function toggleBeneficiaryPeriodEnd() {
        const dateRadio = document.getElementById('beneficiaryPeriodEndDate');
        const dateInput = document.getElementById('beneficiaryPeriodEnd');

        if (dateRadio.checked) {
            // 选择具体日期
            dateInput.style.display = 'block';
            dateInput.required = true;
            dateInput.value = '';
        } else {
            // 选择长期
            dateInput.style.display = 'none';
            dateInput.required = false;
            dateInput.value = '长期';
        }
    }

    // 获取受益人证件有效期结束时间
    function getBeneficiaryPeriodEnd() {
        const longTermRadio = document.getElementById('beneficiaryPeriodEndLongTerm');
        if (longTermRadio.checked) {
            return '长期';
        } else {
            return document.getElementById('beneficiaryPeriodEnd').value;
        }
    }

    // 切换主体类型时显示/隐藏相关字段
    function toggleSubjectFields() {
        const subjectType = document.getElementById('subjectType').value;
        const contactType = document.getElementById('contactType').value;
        const idCardAddressField = document.getElementById('idCardAddressField');
        const legalPersonProofField = document.getElementById('legalPersonProofField');
        const legalPersonLabel = document.getElementById('legalPersonLabel');

        // 根据主体类型调整标签
        if (subjectType === 'SUBJECT_TYPE_INDIVIDUAL') {
            legalPersonLabel.textContent = '个体户经营者姓名';
        } else {
            legalPersonLabel.textContent = '法定代表人姓名';
        }

        // 企业类型时显示身份证居住地址
        if (subjectType === 'SUBJECT_TYPE_ENTERPRISE') {
            idCardAddressField.classList.remove('hidden');
            document.getElementById('idCardAddress').required = true;
        } else {
            idCardAddressField.classList.add('hidden');
            document.getElementById('idCardAddress').required = false;
        }

        // 经办人 + 企业类型时显示法定代表人说明图片
        if (contactType === 'SUPER' && subjectType === 'SUBJECT_TYPE_ENTERPRISE') {
            legalPersonProofField.classList.remove('hidden');
            document.getElementById('legalPersonProof').required = true;
        } else {
            legalPersonProofField.classList.add('hidden');
            document.getElementById('legalPersonProof').required = false;
        }
    }

    // 显示指定步骤
    function showStep(step) {
        console.log('显示步骤:', step);

        // 隐藏所有步骤
        for (let i = 1; i <= totalSteps; i++) {
            const stepElement = document.getElementById(`step${i}`);
            if (stepElement) {
                stepElement.classList.add('hidden');
            }

            const indicatorElement = document.getElementById(`step${i}-indicator`);
            if (indicatorElement) {
                indicatorElement.classList.remove('bg-primary', 'text-white');
                indicatorElement.classList.add('bg-gray-300', 'text-gray-500');
            }

            const textElement = document.getElementById(`step${i}-text`);
            if (textElement) {
                textElement.classList.remove('text-primary');
                textElement.classList.add('text-gray-500');
            }
        }

        // 显示当前步骤
        const currentStepElement = document.getElementById(`step${step}`);
        if (currentStepElement) {
            currentStepElement.classList.remove('hidden');
            console.log(`步骤${step}已显示`);
        } else {
            console.error(`找不到步骤${step}的元素`);
        }

        const currentIndicatorElement = document.getElementById(`step${step}-indicator`);
        if (currentIndicatorElement) {
            currentIndicatorElement.classList.remove('bg-gray-300', 'text-gray-500');
            currentIndicatorElement.classList.add('bg-primary', 'text-white');
        }

        const currentTextElement = document.getElementById(`step${step}-text`);
        if (currentTextElement) {
            currentTextElement.classList.remove('text-gray-500');
            currentTextElement.classList.add('text-primary');
        }

        // 更新按钮状态
        if (step === 1) {
            prevBtn.classList.add('hidden');
        } else {
            prevBtn.classList.remove('hidden');
        }

        if (step === totalSteps) {
            nextBtn.classList.add('hidden');
            submitMerchantApplyBtn.classList.remove('hidden');
        } else {
            nextBtn.classList.remove('hidden');
            submitMerchantApplyBtn.classList.add('hidden');
        }

        // 滚动到顶部
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    // 验证当前步骤
    function validateCurrentStep() {
        const formData = new FormData(merchantApplyForm);

        switch (currentStep) {
            case 1: // 超级管理员信息
                return validateStep1(formData);
            case 2: // 主体资料
                return validateStep2(formData);
            case 3: // 身份证件信息
                return validateStep3(formData);
            case 4: // 最终受益人信息
                return validateStep4(formData);
            case 5: // 经营资料
                return validateStep5(formData);
            case 6: // 银行账户
                return validateStep6(formData);
            default:
                return true;
        }
    }

    // 验证第1步：超级管理员信息
    function validateStep1(formData) {
        const requiredFields = ['contactType', 'contactName', 'mobilePhone', 'contactEmail'];

        for (const field of requiredFields) {
            if (!formData.get(field)) {
                showError(`请填写${getFieldLabel(field)}`);
                // 聚焦到未填写的字段
                const fieldElement = document.getElementById(field);
                if (fieldElement) {
                    fieldElement.focus();
                    fieldElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
                return false;
            }
        }

        // 验证邮箱格式
        const email = formData.get('contactEmail');
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            showError('请输入正确的邮箱格式');
            document.getElementById('contactEmail').focus();
            return false;
        }

        // 验证手机号格式
        const phone = formData.get('mobilePhone');
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(phone)) {
            showError('请输入正确的手机号码格式');
            document.getElementById('mobilePhone').focus();
            return false;
        }

        // 如果是经办人，验证额外字段
        const contactType = formData.get('contactType');
        if (contactType === 'SUPER') {
            const contactExtraFields = ['contactIdDocType', 'contactIdNumber', 'contactPeriodBegin'];

            for (const field of contactExtraFields) {
                if (!formData.get(field)) {
                    showError(`请填写${getFieldLabel(field)}`);
                    return false;
                }
            }

            // 验证经办人证件有效期
            const contactPeriodEnd = getContactPeriodEnd();
            if (!contactPeriodEnd) {
                showError('请选择经办人证件有效期结束时间');
                return false;
            }

            // 验证经办人证件照片
            const contactIdDocCopyMediaId = document.getElementById('contactIdDocCopy').dataset.mediaId;
            const contactIdDocCopyBackMediaId = document.getElementById('contactIdDocCopyBack').dataset.mediaId;
            const businessAuthorizationLetterMediaId = document.getElementById('businessAuthorizationLetter').dataset.mediaId;

            if (!contactIdDocCopyMediaId) {
                showError('请上传经办人证件正面照片并等待上传完成');
                return false;
            }

            if (!contactIdDocCopyBackMediaId) {
                showError('请上传经办人证件反面照片并等待上传完成');
                return false;
            }

            if (!businessAuthorizationLetterMediaId) {
                showError('请上传业务办理授权函并等待上传完成');
                return false;
            }
        }

        return true;
    }

    // 验证第2步：主体资料
    function validateStep2(formData) {
        const requiredFields = ['subjectType', 'licenseNumber', 'merchantName', 'legalPerson', 'registeredAddress', 'licensePeriodBegin'];

        for (const field of requiredFields) {
            if (!formData.get(field)) {
                showError(`请填写${getFieldLabel(field)}`);
                // 聚焦到未填写的字段
                const fieldElement = document.getElementById(field);
                if (fieldElement) {
                    fieldElement.focus();
                    fieldElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
                return false;
            }
        }

        // 验证统一社会信用代码格式（18位）
        const licenseNumber = formData.get('licenseNumber');
        if (licenseNumber.length !== 18) {
            showError('统一社会信用代码应为18位');
            document.getElementById('licenseNumber').focus();
            return false;
        }

        // 验证营业执照有效期
        const licensePeriodEnd = getLicensePeriodEnd();
        if (!licensePeriodEnd) {
            showError('请选择营业执照有效期结束时间');
            return false;
        }

        // 验证营业执照照片
        const licenseCopyMediaId = document.getElementById('licenseCopy').dataset.mediaId;
        if (!licenseCopyMediaId) {
            showError('请上传营业执照照片并等待上传完成');
            return false;
        }

        return true;
    }

    // 验证第3步：身份证件信息
    function validateStep3(formData) {
        const requiredFields = ['idCardName', 'idCardNumber', 'cardPeriodBegin'];

        for (const field of requiredFields) {
            if (!formData.get(field)) {
                showError(`请填写${getFieldLabel(field)}`);
                // 聚焦到未填写的字段
                const fieldElement = document.getElementById(field);
                if (fieldElement) {
                    fieldElement.focus();
                    fieldElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
                return false;
            }
        }

        // 验证身份证号码格式
        const idCardNumber = formData.get('idCardNumber');
        const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
        if (!idCardRegex.test(idCardNumber)) {
            showError('请输入正确的身份证号码格式');
            document.getElementById('idCardNumber').focus();
            return false;
        }

        // 验证身份证有效期
        const cardPeriodEnd = getCardPeriodEnd();
        if (!cardPeriodEnd) {
            showError('请选择身份证有效期结束时间');
            return false;
        }

        // 验证身份证照片
        const idCardCopyMediaId = document.getElementById('idCardCopy').dataset.mediaId;
        const idCardNationalMediaId = document.getElementById('idCardNational').dataset.mediaId;

        if (!idCardCopyMediaId) {
            showError('请上传身份证人像面照片并等待上传完成');
            return false;
        }

        if (!idCardNationalMediaId) {
            showError('请上传身份证国徽面照片并等待上传完成');
            return false;
        }

        // 验证企业类型的额外字段
        const subjectType = formData.get('subjectType');
        if (subjectType === 'SUBJECT_TYPE_ENTERPRISE') {
            if (!formData.get('idCardAddress')) {
                showError('企业类型需要填写身份证居住地址');
                return false;
            }

            // 验证法定代表人说明图片（企业 + 经办人时需要）
            const contactType = formData.get('contactType');
            if (contactType === 'SUPER') {
                const legalPersonProofMediaId = document.getElementById('legalPersonProof').dataset.mediaId;
                if (!legalPersonProofMediaId) {
                    showError('请上传法定代表人说明图片并等待上传完成');
                    return false;
                }
            }
        }

        return true;
    }

    // 验证第4步：最终受益人信息
    function validateStep4(formData) {
        const requiredFields = ['beneficiaryIdDocType', 'beneficiaryName', 'beneficiaryIdNumber', 'beneficiaryAddress', 'beneficiaryPeriodBegin'];

        for (const field of requiredFields) {
            if (!formData.get(field)) {
                showError(`请填写${getFieldLabel(field)}`);
                // 聚焦到未填写的字段
                const fieldElement = document.getElementById(field);
                if (fieldElement) {
                    fieldElement.focus();
                    fieldElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
                return false;
            }
        }

        // 如果受益人证件类型是身份证，验证格式
        const beneficiaryIdDocType = formData.get('beneficiaryIdDocType');
        const beneficiaryIdNumber = formData.get('beneficiaryIdNumber');
        if (beneficiaryIdDocType === 'IDENTIFICATION_TYPE_IDCARD') {
            const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
            if (!idCardRegex.test(beneficiaryIdNumber)) {
                showError('请输入正确的受益人身份证号码格式');
                document.getElementById('beneficiaryIdNumber').focus();
                return false;
            }
        }

        // 验证受益人证件有效期
        const beneficiaryPeriodEnd = getBeneficiaryPeriodEnd();
        if (!beneficiaryPeriodEnd) {
            showError('请选择受益人证件有效期结束时间');
            return false;
        }

        // 验证受益人证件照片
        const beneficiaryIdDocCopyMediaId = document.getElementById('beneficiaryIdDocCopy').dataset.mediaId;
        const beneficiaryIdDocCopyBackMediaId = document.getElementById('beneficiaryIdDocCopyBack').dataset.mediaId;

        if (!beneficiaryIdDocCopyMediaId) {
            showError('请上传受益人证件正面照片并等待上传完成');
            return false;
        }

        if (!beneficiaryIdDocCopyBackMediaId) {
            showError('请上传受益人证件反面照片并等待上传完成');
            return false;
        }

        return true;
    }

    // 验证第5步：经营资料
    function validateStep5(formData) {
        const requiredFields = ['merchantShortname', 'servicePhone'];

        for (const field of requiredFields) {
            if (!formData.get(field)) {
                showError(`请填写${getFieldLabel(field)}`);
                // 聚焦到未填写的字段
                const fieldElement = document.getElementById(field);
                if (fieldElement) {
                    fieldElement.focus();
                    fieldElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
                return false;
            }
        }

        // 验证客服电话格式
        const servicePhone = formData.get('servicePhone');
        const phoneRegex = /^(\d{3,4}-?\d{7,8}|\d{11})$/;
        if (!phoneRegex.test(servicePhone)) {
            showError('请输入正确的客服电话格式（如：010-12345678 或 13812345678）');
            document.getElementById('servicePhone').focus();
            return false;
        }

        // 验证商户简称长度
        const merchantShortname = formData.get('merchantShortname');
        if (merchantShortname.length < 2 || merchantShortname.length > 64) {
            showError('商户简称长度应在2-64个字符之间');
            document.getElementById('merchantShortname').focus();
            return false;
        }

        // 验证经营场景类型
        const salesScenesTypes = formData.getAll('salesScenesType');
        if (salesScenesTypes.length === 0) {
            showError('请至少选择一个经营场景类型');
            // 滚动到经营场景选择区域
            const scenesElement = document.querySelector('input[name="salesScenesType"]');
            if (scenesElement) {
                scenesElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
            return false;
        }

        return true;
    }

    // 验证第6步：银行账户
    function validateStep6(formData) {
        const requiredFields = ['bankAccountType', 'accountName', 'accountBank', 'accountNumber'];

        for (const field of requiredFields) {
            if (!formData.get(field)) {
                showError(`请填写${getFieldLabel(field)}`);
                // 聚焦到未填写的字段
                const fieldElement = document.getElementById(field);
                if (fieldElement) {
                    fieldElement.focus();
                    fieldElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
                return false;
            }
        }

        // 验证银行账号格式（基本验证：16-19位数字）
        const accountNumber = formData.get('accountNumber');
        const accountRegex = /^\d{16,19}$/;
        if (!accountRegex.test(accountNumber)) {
            showError('请输入正确的银行账号格式（16-19位数字）');
            document.getElementById('accountNumber').focus();
            return false;
        }

        return true;
    }

    // 添加实时验证
    function addRealTimeValidation() {
        // 邮箱实时验证
        document.getElementById('contactEmail').addEventListener('blur', function() {
            const email = this.value;
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (email && !emailRegex.test(email)) {
                showFieldError(this, '请输入正确的邮箱格式');
            } else {
                clearFieldError(this);
            }
        });

        // 手机号实时验证
        document.getElementById('mobilePhone').addEventListener('blur', function() {
            const phone = this.value;
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (phone && !phoneRegex.test(phone)) {
                showFieldError(this, '请输入正确的手机号码格式');
            } else {
                clearFieldError(this);
            }
        });

        // 身份证号实时验证
        document.getElementById('idCardNumber').addEventListener('blur', function() {
            const idCard = this.value;
            const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
            if (idCard && !idCardRegex.test(idCard)) {
                showFieldError(this, '请输入正确的身份证号码格式');
            } else {
                clearFieldError(this);
            }
        });

        // 统一社会信用代码实时验证
        document.getElementById('licenseNumber').addEventListener('blur', function() {
            const license = this.value;
            if (license && license.length !== 18) {
                showFieldError(this, '统一社会信用代码应为18位');
            } else {
                clearFieldError(this);
            }
        });

        // 银行账号实时验证
        document.getElementById('accountNumber').addEventListener('blur', function() {
            const account = this.value;
            const accountRegex = /^\d{16,19}$/;
            if (account && !accountRegex.test(account)) {
                showFieldError(this, '请输入正确的银行账号格式（16-19位数字）');
            } else {
                clearFieldError(this);
            }
        });

        // 商户简称实时验证
        document.getElementById('merchantShortname').addEventListener('blur', function() {
            const shortname = this.value;
            if (shortname && (shortname.length < 2 || shortname.length > 64)) {
                showFieldError(this, '商户简称长度应在2-64个字符之间');
            } else {
                clearFieldError(this);
            }
        });
    }

    // 显示字段错误
    function showFieldError(field, message) {
        clearFieldError(field);

        field.classList.add('border-red-500');

        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error text-red-500 text-xs mt-1';
        errorDiv.textContent = message;

        field.parentNode.appendChild(errorDiv);
    }

    // 清除字段错误
    function clearFieldError(field) {
        field.classList.remove('border-red-500');

        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
    }

    // 改进showError函数，添加更明显的提示
    function showError(message) {
        // 移除之前的错误提示
        const existingError = document.querySelector('.step-error-message');
        if (existingError) {
            existingError.remove();
        }

        // 创建新的错误提示
        const errorDiv = document.createElement('div');
        errorDiv.className = 'step-error-message bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4';
        errorDiv.innerHTML = `
            <div class="flex items-center">
                <i class="fa fa-exclamation-triangle mr-2"></i>
                <span>${message}</span>
            </div>
        `;

        // 插入到当前步骤的顶部
        const currentStepElement = document.getElementById(`step${currentStep}`);
        if (currentStepElement) {
            currentStepElement.insertBefore(errorDiv, currentStepElement.firstChild);
            errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        // 3秒后自动消失
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 5000);
    }

    // 页面加载完成后初始化RSA加密
    document.addEventListener('DOMContentLoaded', async function() {
        console.log('页面加载完成，开始初始化RSA加密...');
        try {
            const success = await rsaEncryption.initialize();
            if (success) {
                console.log('RSA加密初始化成功');
            } else {
                console.warn('RSA加密初始化失败，将使用明文传输');
            }
        } catch (error) {
            console.error('RSA加密初始化异常:', error);
        }
    });
</script>
</body>
</html>


