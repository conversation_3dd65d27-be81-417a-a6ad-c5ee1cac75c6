<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RSA加密测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- RSA加密库 -->
    <script src="https://cdn.jsdelivr.net/npm/jsencrypt@3.3.2/bin/jsencrypt.min.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8">RSA加密功能测试</h1>
        
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">1. 公钥获取测试</h2>
            <button id="getPublicKeyBtn" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                获取公钥
            </button>
            <div id="publicKeyResult" class="mt-4 p-4 bg-gray-50 rounded hidden">
                <h3 class="font-semibold">公钥信息：</h3>
                <div id="publicKeyContent" class="text-sm text-gray-600 mt-2"></div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">2. 加密功能测试</h2>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">输入要加密的文本：</label>
                <input type="text" id="plainText" class="w-full p-2 border border-gray-300 rounded" 
                       placeholder="例如：张三" value="张三">
            </div>
            <button id="encryptBtn" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                加密文本
            </button>
            <div id="encryptResult" class="mt-4 p-4 bg-gray-50 rounded hidden">
                <h3 class="font-semibold">加密结果：</h3>
                <div id="encryptedContent" class="text-sm text-gray-600 mt-2 break-all"></div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">3. 批量加密测试</h2>
            <button id="batchEncryptBtn" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                批量加密测试数据
            </button>
            <div id="batchResult" class="mt-4 p-4 bg-gray-50 rounded hidden">
                <h3 class="font-semibold">批量加密结果：</h3>
                <div id="batchContent" class="text-sm text-gray-600 mt-2"></div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">4. 网络传输测试</h2>
            <p class="text-gray-600 mb-4">测试加密数据通过HTTP传输的安全性</p>
            <button id="networkTestBtn" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                模拟网络传输
            </button>
            <div id="networkResult" class="mt-4 p-4 bg-gray-50 rounded hidden">
                <h3 class="font-semibold">传输结果：</h3>
                <div id="networkContent" class="text-sm text-gray-600 mt-2"></div>
            </div>
        </div>
    </div>

    <script>
        // RSA加密配置
        const RSA_CONFIG = {
            publicKey: null,
            publicKeyId: null,
            sensitiveFields: [
                'contactName', 'mobilePhone', 'contactEmail', 'idCardName', 'idCardNumber',
                'legalPerson', 'beneficiaryName', 'beneficiaryIdNumber', 'beneficiaryAddress',
                'accountName', 'accountNumber', 'agentIdNumber', 'agentName'
            ]
        };

        // RSA加密工具类
        class RSAEncryption {
            constructor() {
                this.encrypt = new JSEncrypt();
                this.initialized = false;
            }

            async initialize() {
                if (this.initialized) {
                    return true;
                }

                try {
                    const response = await fetch('http://127.0.0.1:8081/api/wx-pay/public-key', {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`获取公钥失败，状态码: ${response.status}`);
                    }

                    const data = await response.json();

                    if (data.code !== 200) {
                        throw new Error(data.msg || '获取公钥失败');
                    }

                    RSA_CONFIG.publicKey = data.data.publicKey;
                    RSA_CONFIG.publicKeyId = data.data.publicKeyId;
                    this.encrypt.setPublicKey(RSA_CONFIG.publicKey);
                    this.initialized = true;

                    console.log('RSA公钥初始化成功，公钥ID:', RSA_CONFIG.publicKeyId);
                    return true;

                } catch (error) {
                    console.error('获取公钥失败:', error);
                    return false;
                }
            }

            async encryptString(plainText) {
                if (!plainText || typeof plainText !== 'string') {
                    return plainText;
                }

                if (!this.initialized) {
                    const success = await this.initialize();
                    if (!success) {
                        console.warn('RSA未初始化，跳过加密');
                        return plainText;
                    }
                }
                
                try {
                    const utf8Length = new TextEncoder().encode(plainText).length;
                    if (utf8Length > 214) {
                        console.warn(`字符串长度超过RSA加密限制: ${utf8Length} 字节`);
                        return plainText;
                    }
                    
                    const encrypted = this.encrypt.encrypt(plainText);
                    if (!encrypted) {
                        console.error('RSA加密失败');
                        return plainText;
                    }
                    
                    console.log(`已加密字段，原文长度: ${utf8Length} 字节`);
                    return encrypted;
                } catch (error) {
                    console.error('RSA加密异常:', error);
                    return plainText;
                }
            }

            async encryptSensitiveData(data) {
                if (!data || typeof data !== 'object') {
                    return data;
                }

                if (!this.initialized) {
                    const success = await this.initialize();
                    if (!success) {
                        console.warn('RSA未初始化，跳过加密');
                        return data;
                    }
                }

                const encryptedData = { ...data };
                
                const encryptPromises = RSA_CONFIG.sensitiveFields.map(async field => {
                    if (encryptedData[field]) {
                        const originalValue = encryptedData[field];
                        const encryptedValue = await this.encryptString(originalValue);
                        if (encryptedValue !== originalValue) {
                            encryptedData[field] = encryptedValue;
                            console.log(`已加密字段: ${field}`);
                        }
                    }
                });

                await Promise.all(encryptPromises);
                return encryptedData;
            }
        }

        // 创建全局RSA加密实例
        const rsaEncryption = new RSAEncryption();

        // 事件处理
        document.getElementById('getPublicKeyBtn').addEventListener('click', async () => {
            try {
                const success = await rsaEncryption.initialize();
                const resultDiv = document.getElementById('publicKeyResult');
                const contentDiv = document.getElementById('publicKeyContent');
                
                if (success) {
                    contentDiv.innerHTML = `
                        <p><strong>公钥ID:</strong> ${RSA_CONFIG.publicKeyId}</p>
                        <p><strong>公钥内容:</strong></p>
                        <pre class="text-xs bg-white p-2 rounded border mt-2">${RSA_CONFIG.publicKey}</pre>
                    `;
                    resultDiv.classList.remove('hidden');
                } else {
                    contentDiv.innerHTML = '<p class="text-red-500">获取公钥失败</p>';
                    resultDiv.classList.remove('hidden');
                }
            } catch (error) {
                console.error('获取公钥异常:', error);
            }
        });

        document.getElementById('encryptBtn').addEventListener('click', async () => {
            const plainText = document.getElementById('plainText').value;
            if (!plainText) {
                alert('请输入要加密的文本');
                return;
            }

            try {
                const encrypted = await rsaEncryption.encryptString(plainText);
                const resultDiv = document.getElementById('encryptResult');
                const contentDiv = document.getElementById('encryptedContent');
                
                contentDiv.innerHTML = `
                    <p><strong>原文:</strong> ${plainText}</p>
                    <p><strong>原文长度:</strong> ${new TextEncoder().encode(plainText).length} 字节</p>
                    <p><strong>加密结果:</strong> ${encrypted}</p>
                    <p><strong>加密后长度:</strong> ${encrypted.length} 字符</p>
                    <p><strong>是否已加密:</strong> ${encrypted !== plainText ? '是' : '否'}</p>
                `;
                resultDiv.classList.remove('hidden');
            } catch (error) {
                console.error('加密异常:', error);
            }
        });

        document.getElementById('batchEncryptBtn').addEventListener('click', async () => {
            const testData = {
                contactName: '张三',
                mobilePhone: '***********',
                contactEmail: '<EMAIL>',
                idCardName: '张三',
                idCardNumber: '110101199001011234',
                legalPerson: '李四',
                accountName: '张三',
                accountNumber: '6222021234567890123',
                normalField: '这个字段不会被加密'
            };

            try {
                const encrypted = await rsaEncryption.encryptSensitiveData(testData);
                const resultDiv = document.getElementById('batchResult');
                const contentDiv = document.getElementById('batchContent');
                
                let html = '<h4 class="font-semibold mb-2">加密前后对比：</h4>';
                for (const [key, value] of Object.entries(testData)) {
                    const encryptedValue = encrypted[key];
                    const isEncrypted = encryptedValue !== value;
                    html += `
                        <div class="mb-2 p-2 border rounded">
                            <p><strong>${key}:</strong></p>
                            <p class="text-sm">原文: ${value}</p>
                            <p class="text-sm ${isEncrypted ? 'text-green-600' : 'text-gray-600'}">
                                ${isEncrypted ? '加密后' : '未加密'}: ${encryptedValue.length > 50 ? encryptedValue.substring(0, 50) + '...' : encryptedValue}
                            </p>
                        </div>
                    `;
                }
                
                contentDiv.innerHTML = html;
                resultDiv.classList.remove('hidden');
            } catch (error) {
                console.error('批量加密异常:', error);
            }
        });

        document.getElementById('networkTestBtn').addEventListener('click', async () => {
            const testData = {
                contactName: '网络传输测试用户',
                mobilePhone: '13900139000',
                contactEmail: '<EMAIL>'
            };

            try {
                // 加密数据
                const encryptedData = await rsaEncryption.encryptSensitiveData(testData);
                
                // 模拟网络传输（显示传输的数据）
                const resultDiv = document.getElementById('networkResult');
                const contentDiv = document.getElementById('networkContent');
                
                contentDiv.innerHTML = `
                    <h4 class="font-semibold mb-2">网络传输数据预览：</h4>
                    <div class="bg-white p-3 rounded border">
                        <p class="text-sm font-medium text-green-600">✓ 敏感数据已加密，网络传输安全</p>
                        <pre class="text-xs mt-2 bg-gray-100 p-2 rounded">${JSON.stringify(encryptedData, null, 2)}</pre>
                    </div>
                    <div class="mt-4 p-3 bg-blue-50 rounded">
                        <p class="text-sm"><strong>安全说明：</strong></p>
                        <ul class="text-xs text-gray-600 mt-1 list-disc list-inside">
                            <li>敏感字段已使用RSA公钥加密</li>
                            <li>即使HTTP传输被截获，敏感信息也无法被解读</li>
                            <li>只有拥有对应私钥的微信支付服务器才能解密</li>
                        </ul>
                    </div>
                `;
                resultDiv.classList.remove('hidden');
            } catch (error) {
                console.error('网络测试异常:', error);
            }
        });
    </script>
</body>
</html>
