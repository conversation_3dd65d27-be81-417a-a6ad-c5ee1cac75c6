package cn.jiawen.payment.controller;


import cn.jiawen.payment.entity.OrderInfo;
import cn.jiawen.payment.enums.OrderStatus;
import cn.jiawen.payment.service.OrderInfoService;
import cn.jiawen.payment.strategy.PaymentInfoStrategy;
import cn.jiawen.payment.strategy.PaymentInfoStrategyFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import vo.AjaxResult;
import vo.HttpStatus;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@CrossOrigin
@RestController
@RequestMapping("/api/order-info")
@Api(tags = "商品订单管理")
public class OrderInfoController {

    @Resource
    private OrderInfoService orderInfoService;

    @Resource
    private PaymentInfoStrategyFactory strategyFactory;

    /*
    * 查询所有订单列表
    * */
    @ApiOperation("订单列表")
    @GetMapping("/list")
    public AjaxResult list(){

        List<OrderInfo> list = orderInfoService.listOrderByCreateTimeDesc();

        return AjaxResult.success(list);
    }

    /*
     * TODO: 预留接口 查询本地订单状态 前端二维码支付完成跳转可调用此接口
     * */
    @ApiOperation("查询本地订单状态")
    @GetMapping("/query-order-status/{orderNo}")
    public AjaxResult queryOrderStatus(@PathVariable String orderNo){

        String orderStatus = orderInfoService.getOrderStatus(orderNo);
        if(OrderStatus.SUCCESS.getType().equals(orderStatus)){
            return AjaxResult.success("支付成功");
        }

        return new AjaxResult(HttpStatus.NO_CONTENT, "支付中");
    }

    /**
     * 统一退款接口
     * @param orderNo 订单号
     * @param reason 退款原因
     * @return 退款结果
     */
    @ApiOperation("申请退款")
    @PostMapping("/refund/{orderNo}/{reason}")
    public AjaxResult refund(@PathVariable String orderNo, @PathVariable String reason) {

        log.info("统一退款接口调用，订单号：{}，退款原因：{}", orderNo, reason);

        try {
            // 根据订单号获取订单信息
            OrderInfo orderInfo = orderInfoService.getOrderByOrderNo(orderNo);
            if (orderInfo == null) {
                return AjaxResult.error("订单不存在");
            }

            // 检查订单状态
            if (!OrderStatus.SUCCESS.getType().equals(orderInfo.getOrderStatus())) {
                return AjaxResult.error("订单状态不支持退款");
            }

            // 获取支付方式
            String paymentType = orderInfo.getPaymentType();
            if (paymentType == null) {
                return AjaxResult.error("订单支付方式未知");
            }

            log.info("订单支付方式：{}", paymentType);

            // 根据支付方式获取对应的策略
            PaymentInfoStrategy strategy = strategyFactory.getStrategy(paymentType);

            // 使用策略处理退款
            strategy.processRefund(orderNo, reason);

            log.info("退款申请成功，订单号：{}", orderNo);
            return AjaxResult.success("退款申请成功");

        } catch (IllegalArgumentException e) {
            log.error("不支持的支付方式，订单号：{}，支付方式：{}", orderNo, e.getMessage());
            return AjaxResult.error("不支持的支付方式：" + e.getMessage());
        } catch (RuntimeException e) {
            log.error("退款申请失败，订单号：{}，原因：{}", orderNo, e.getMessage(), e);

            // 提供更友好的错误信息
            String errorMsg = e.getMessage();
            if (errorMsg != null && errorMsg.contains("系统繁忙")) {
                return AjaxResult.error("支付系统繁忙，请稍后重试");
            } else if (errorMsg != null && errorMsg.contains("暂时不可用")) {
                return AjaxResult.error("退款服务暂时不可用，请稍后重试");
            } else {
                return AjaxResult.error("退款申请失败：" + errorMsg);
            }
        } catch (Exception e) {
            log.error("退款申请异常，订单号：{}，原因：{}", orderNo, e.getMessage(), e);
            return AjaxResult.error("系统异常，请稍后重试");
        }
    }

}
