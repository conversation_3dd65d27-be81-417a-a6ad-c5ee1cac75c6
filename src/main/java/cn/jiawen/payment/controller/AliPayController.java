package cn.jiawen.payment.controller;

import cn.jiawen.payment.entity.OrderInfo;
import cn.jiawen.payment.service.AliPayService;
import cn.jiawen.payment.service.OrderInfoService;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayConstants;
import com.alipay.api.internal.util.AlipaySignature;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.*;
import vo.AjaxResult;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Map;

@Slf4j
@CrossOrigin
@Api(tags = "网站支付宝支付")
@RestController
@RequestMapping("/api/ali-pay")
public class AliPayController {

    @Resource
    private AliPayService aliPayService;

    @Resource
    private OrderInfoService orderInfoService;

    @Resource
    private Environment config;

    @ApiOperation("统一收单下单并支付页面接口")
    @PostMapping("/trade/page/pay/{productId}")
    // https://opendocs.alipay.com/open/270/105902?pathHash=d5cd617e
    public AjaxResult tradePagePay(@PathVariable Long productId){

        log.info("统一收单下单并支付页面接口调用");

        String formStr = aliPayService.tradeCreate(productId);
        // form表单字符串提交给前端 前端调用自动提交脚本，进行表单提交
        return AjaxResult.success("", formStr);
    }

    @ApiOperation("支付回调通知")
    @PostMapping("/trade/notify")
    public String tradeNotify(@RequestParam Map<String,String> params){

        log.info("支付回调接口调用，回调参数 ===> {}", params);

        String res = "failure";

        try {
            // 异步通知验签
            boolean signVerified = AlipaySignature.rsaCheckV1(params,
                    config.getProperty("alipay.alipay-public-key"),
                    AlipayConstants.CHARSET_UTF8,
                    AlipayConstants.SIGN_TYPE_RSA2); // 调用SDK验证签名

            if(!signVerified){
                // 验签失败则记录异常日志，并在response中返回failure.
                log.error("支付成功异步验签失败");

                return res;
            }
            // 验签成功按照支付结果异步通知中的描述，对支付结果中的业务内容进行二次校验，
            // 1、商家需要验证该通知数据中的 out_trade_no 是否为商家系统中创建的订单号
            String outTradeNo = params.get("out_trade_no");
            OrderInfo order = orderInfoService.getOrderByOrderNo(outTradeNo);
            if(order == null){
                log.error("订单不存在");
                return res;
            }

            // 2、判断 total_amount 是否确实为该订单的实际金额（即商家订单创建时的金额）。
            String totalAmount = params.get("total_amount");
            int totalAmountInt = new BigDecimal(totalAmount).multiply(new BigDecimal(100)).intValue();
            if(totalAmountInt != order.getTotalFee().intValue()){
                log.error("订单金额校验失败");
                return res;
            }

            // 3、校验通知中的 seller_id（或者 seller_email）是否为 out_trade_no 这笔单据的对应的操作方
            String sellerId = params.get("seller_id");
            if(!sellerId.equals(config.getProperty("alipay.seller-id"))){
                log.error("商家pid校验失败");
                return res;
            }

            // 4、验证 app_id 是否为该商家本身
            String appId = params.get("app_id");
            if(!appId.equals(config.getProperty("alipay.app-id"))){
                log.error("appid校验失败");
                return res;
            }

            // 在支付宝的业务通知中，只有交易通知状态为 TRADE_SUCCESS 时，支付宝才会认定为买家付款成功。
            String tradeStatus = params.get("trade_status");
            if (!"TRADE_SUCCESS".equals(tradeStatus)) {
                log.error("支付未成功");
                return res;
            }

            // 修改订单状态，记录支付日志
            aliPayService.processOrder(params);

            // 校验成功后在response中返回success并继续商户自身业务处理，校验失败返回failure
            log.info("支付成功异步验签成功");
            res = "success";
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return res;
    }

    @ApiOperation("用户取消订单")
    @PostMapping("/trade/close/{orderNo}")
    public AjaxResult cancel(@PathVariable String orderNo){

        log.info("取消订单");
        aliPayService.cancelOrder(orderNo);

        return AjaxResult.success();

    }


    @ApiOperation("查询订单：测试用")  // 该接口由定时任务自动调用
    @GetMapping("/trade/query/{orderNo}")
    public AjaxResult queryOrder(@PathVariable String orderNo){

        log.info("查询订单");

        String res = aliPayService.queryOrder(orderNo);

        if (res == null){
            return AjaxResult.success("查询成功", "订单不存在");
        }

        return AjaxResult.success("查询成功", res);
    }


    @ApiOperation("查询退款：测试用") // 该接口由定时任务自动调用
    @PostMapping("/trade/fastpay/refund/query/{refundNo}")
    public AjaxResult queryRefund(@PathVariable String refundNo){

        log.info("查询退款");

        String res = aliPayService.queryRefund(refundNo);

        return AjaxResult.success("查询成功", res);
    }

    @ApiOperation("获取账单url")
    @GetMapping("/bill/downloadurl/query/{billDate}/{type}")
    public AjaxResult queryTradeBill(
            @PathVariable String billDate,
            @PathVariable String type)  {

        log.info("获取账单url");
        String downloadUrl = aliPayService.queryBill(billDate, type);

        return AjaxResult.success("获取账单url成功", downloadUrl);
    }
}


