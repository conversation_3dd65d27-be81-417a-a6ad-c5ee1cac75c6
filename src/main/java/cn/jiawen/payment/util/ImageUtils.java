package cn.jiawen.payment.util;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 图片处理工具类
 * 提供图片转二进制和SHA256计算功能
 */
public class ImageUtils {

    /**
     * 计算字节数组的SHA256哈希值
     *
     * @param data 要计算哈希的字节数组
     * @return SHA256哈希值的十六进制字符串
     * @throws NoSuchAlgorithmException SHA256算法不可用
     */
    public static String calculateSha256(byte[] data) throws NoSuchAlgorithmException {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hashBytes = digest.digest(data);

        StringBuilder hexString = new StringBuilder();
        for (byte b : hashBytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }

        return hexString.toString();
    }


}
