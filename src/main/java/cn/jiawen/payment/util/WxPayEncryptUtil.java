package cn.jiawen.payment.util;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.util.Base64;

/**
 * 微信支付公钥加密工具类
 */
@Slf4j
public class WxPayEncryptUtil {

    /**
     * 使用微信支付公钥进行RSA OAEP加密
     * 
     * @param message 待加密的明文字符串
     * @param publicKey 微信支付公钥
     * @return Base64编码的加密结果
     * @throws IllegalBlockSizeException 当加密原串长度超过214字节时抛出
     * @throws IOException IO异常
     */
    public static String rsaEncryptOAEP(String message, PublicKey publicKey) throws IllegalBlockSizeException, IOException {
        try {
            // 使用RSA/ECB/OAEPWithSHA-1AndMGF1Padding算法
            Cipher cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA-1AndMGF1Padding");
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            
            // 将字符串转换为UTF-8字节数组
            byte[] data = message.getBytes("utf-8");
            
            // 执行加密
            byte[] cipherdata = cipher.doFinal(data);
            
            // 返回Base64编码的结果
            return Base64.getEncoder().encodeToString(cipherdata);
            
        } catch (NoSuchAlgorithmException | NoSuchPaddingException e) {
            throw new RuntimeException("当前Java环境不支持RSA OAEP加密算法", e);
        } catch (InvalidKeyException e) {
            throw new IllegalArgumentException("无效的微信支付公钥", e);
        } catch (IllegalBlockSizeException | BadPaddingException e) {
            throw new IllegalBlockSizeException("加密原串的长度不能超过214字节");
        }
    }

    /**
     * 加密敏感信息（如姓名、身份证号等）
     * 
     * @param sensitiveInfo 敏感信息明文
     * @param publicKey 微信支付公钥
     * @return 加密后的Base64字符串
     */
    public static String encryptSensitiveInfo(String sensitiveInfo, PublicKey publicKey) {
        if (sensitiveInfo == null || sensitiveInfo.trim().isEmpty()) {
            return null;
        }
        
        try {
            log.info("开始加密敏感信息，原文长度: {} 字节", sensitiveInfo.getBytes("utf-8").length);
            String encrypted = rsaEncryptOAEP(sensitiveInfo, publicKey);
            log.info("敏感信息加密完成");
            return encrypted;
        } catch (Exception e) {
            log.error("敏感信息加密失败: {}", e.getMessage(), e);
            throw new RuntimeException("敏感信息加密失败", e);
        }
    }

    /**
     * 验证字符串长度是否适合加密
     * RSA OAEP加密对明文长度有限制，通常不能超过214字节
     * 
     * @param message 待验证的字符串
     * @return true表示长度合适，false表示超长
     */
    public static boolean isValidLength(String message) {
        if (message == null) {
            return true;
        }
        
        try {
            byte[] data = message.getBytes("utf-8");
            // RSA 2048位密钥，OAEP填充，最大明文长度约为214字节
            return data.length <= 214;
        } catch (Exception e) {
            log.error("验证字符串长度时发生错误: {}", e.getMessage());
            return false;
        }
    }


}
