package cn.jiawen.payment.util;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import java.util.regex.Pattern;

/**
 * 业务申请编号工具类
 * 
 * 业务申请编号规则：
 * 1、只能由数字、字母或下划线组成，建议前缀为服务商商户号；
 * 2、服务商自定义的唯一编号；
 * 3、每个编号对应一个申请单，每个申请单审核通过后会生成一个微信支付商户号。
 */
public class ApplicationNoUtils {

    /**
     * 业务申请编号的正则表达式：只能包含数字、字母、下划线
     */
    private static final Pattern BUSINESS_APPLICATION_NO_PATTERN = Pattern.compile("^[a-zA-Z0-9_]+$");

    /**
     * 固定前缀：服务商商户号
     */
    private static final String FIXED_PREFIX = "1725024967";

    /**
     * 默认前缀分隔符
     */
    private static final String DEFAULT_SEPARATOR = "_";

    /**
     * 验证业务申请编号格式是否正确
     * 
     * @param businessApplicationNo 业务申请编号
     * @return true-格式正确，false-格式错误
     */
    public static boolean isValidFormat(String businessApplicationNo) {
        if (businessApplicationNo == null || businessApplicationNo.trim().isEmpty()) {
            return false;
        }
        return BUSINESS_APPLICATION_NO_PATTERN.matcher(businessApplicationNo).matches();
    }

    /**
     * 生成业务申请编号（使用固定前缀）
     * 格式：1725024967_yyyyMMddHHmmss_xxx
     *
     * @return 业务申请编号
     */
    public static String generateApplicationNo() {
        return FIXED_PREFIX + DEFAULT_SEPARATOR + getTimestampSuffix() + DEFAULT_SEPARATOR + getRandomSuffix();
    }

    /**
     * 检查业务申请编号是否以固定前缀开头
     *
     * @param businessApplicationNo 业务申请编号
     * @return true-以固定前缀开头，false-不是
     */
    public static boolean hasFixedPrefix(String businessApplicationNo) {
        if (businessApplicationNo == null) {
            return false;
        }
        return businessApplicationNo.startsWith(FIXED_PREFIX + DEFAULT_SEPARATOR);
    }

    /**
     * 从业务申请编号中提取前缀
     *
     * @param businessApplicationNo 业务申请编号
     * @return 前缀，如果格式不正确返回null
     */
    public static String extractPrefix(String businessApplicationNo) {
        if (businessApplicationNo == null || businessApplicationNo.trim().isEmpty()) {
            return null;
        }

        int firstSeparatorIndex = businessApplicationNo.indexOf(DEFAULT_SEPARATOR);
        if (firstSeparatorIndex > 0) {
            return businessApplicationNo.substring(0, firstSeparatorIndex);
        }

        return null;
    }

    /**
     * 获取固定前缀
     *
     * @return 固定前缀
     */
    public static String getFixedPrefix() {
        return FIXED_PREFIX;
    }

    /**
     * 验证业务申请编号是否符合完整规则
     * 包括格式验证和固定前缀验证
     *
     * @param businessApplicationNo 业务申请编号
     * @return true-符合规则，false-不符合
     */
    public static boolean isValidBusinessApplicationNo(String businessApplicationNo) {
        // 首先验证基本格式
        if (!isValidFormat(businessApplicationNo)) {
            return false;
        }

        // 验证是否以固定前缀开头
        return hasFixedPrefix(businessApplicationNo);
    }

    /**
     * 获取时间戳后缀
     * 格式：yyyyMMddHHmm
     * 
     * @return 时间戳字符串
     */
    private static String getTimestampSuffix() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
        return sdf.format(new Date());
    }

    /**
     * 获取随机数后缀
     * 生成3位随机数
     * 
     * @return 3位随机数字符串
     */
    private static String getRandomSuffix() {
        Random random = new Random();
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < 3; i++) {
            result.append(random.nextInt(10));
        }
        return result.toString();
    }

    /**
     * 生成带自定义随机位数的业务申请编号（使用固定前缀）
     *
     * @param randomDigits 随机数位数
     * @return 业务申请编号
     */
    public static String generateApplicationNo(int randomDigits) {
        if (randomDigits < 1 || randomDigits > 10) {
            throw new IllegalArgumentException("随机数位数必须在1-10之间");
        }

        return FIXED_PREFIX + DEFAULT_SEPARATOR + getTimestampSuffix() + DEFAULT_SEPARATOR + getRandomSuffix(randomDigits);
    }

    /**
     * 获取指定位数的随机数后缀
     * 
     * @param digits 位数
     * @return 随机数字符串
     */
    private static String getRandomSuffix(int digits) {
        Random random = new Random();
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < digits; i++) {
            result.append(random.nextInt(10));
        }
        return result.toString();
    }
}
