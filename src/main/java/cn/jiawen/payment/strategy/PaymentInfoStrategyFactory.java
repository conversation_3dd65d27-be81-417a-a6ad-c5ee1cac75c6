package cn.jiawen.payment.strategy;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 支付信息策略工厂
 */
@Slf4j
@Component
public class PaymentInfoStrategyFactory {

    @Autowired
    private List<PaymentInfoStrategy> strategies;

    private Map<String, PaymentInfoStrategy> strategyMap = new HashMap<>();

    @PostConstruct
    public void init() {
        // 初始化策略映射
        for (PaymentInfoStrategy strategy : strategies) {
            strategyMap.put(strategy.getPaymentType(), strategy);
            log.info("注册支付策略：{} -> {}", strategy.getPaymentType(), strategy.getClass().getSimpleName());
        }
    }

    /**
     * 根据支付类型获取对应的策略
     * @return 对应的策略实现
     */
    public PaymentInfoStrategy getStrategy(String paymentType) {
        PaymentInfoStrategy strategy = strategyMap.get(paymentType);
        if (strategy == null) {
            throw new IllegalArgumentException("不支持的支付类型: " + paymentType);
        }
        return strategy;
    }

    /**
     * 获取所有支持的支付类型
     * @return 支付类型列表
     */
    public String[] getSupportedPaymentTypes() {
        return strategyMap.keySet().toArray(new String[0]);
    }
}
