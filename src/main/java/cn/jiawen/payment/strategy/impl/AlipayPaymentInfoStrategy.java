package cn.jiawen.payment.strategy.impl;

import cn.jiawen.payment.entity.PaymentInfo;
import cn.jiawen.payment.enums.PayType;
import cn.jiawen.payment.service.AliPayService;
import cn.jiawen.payment.strategy.PaymentInfoStrategy;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 支付宝支付信息处理策略
 */
@Slf4j
@Component
public class AlipayPaymentInfoStrategy implements PaymentInfoStrategy {

    @Resource
    private AliPayService aliPayService;

    @Override
    public PaymentInfo createPaymentInfo(String orderNo, Map<String, Object> paymentData) {

        log.info("使用支付宝支付策略创建支付信息，订单号：{}", orderNo);
        
        PaymentInfo paymentInfo = new PaymentInfo();
        paymentInfo.setOrderNo(orderNo);
        paymentInfo.setPaymentType(PayType.ALIPAY.getType());
        paymentInfo.setTradeType("电脑网站支付");
        paymentInfo.setTradeState((String) paymentData.get("trade_status"));

        // 订单金额
        String totalAmount = (String) paymentData.get("total_amount");
        int totalAmountInt = new BigDecimal(totalAmount).multiply(new BigDecimal(100)).intValue();
        paymentInfo.setPayerTotal(totalAmountInt);
        
        // 支付宝交易单号
        if (paymentData.containsKey("trade_no")) {
            paymentInfo.setTransactionId((String) paymentData.get("trade_no"));
        }

        // 设置支付内容（支付宝回调的完整数据）
        Gson gson = new Gson();
        String content = gson.toJson(paymentData, HashMap.class);
        paymentInfo.setContent(content);
        
        paymentInfo.setCreateTime(new Date());
        paymentInfo.setUpdateTime(new Date());
        
        log.info("支付宝支付信息创建完成：{}", paymentInfo);
        return paymentInfo;
    }

    @Override
    public void processRefund(String orderNo, String reason) {
        log.info("使用支付宝策略处理退款申请，订单号：{}，退款原因：{}", orderNo, reason);
        aliPayService.refund(orderNo, reason);
    }

    @Override
    public String getPaymentType() {
        return PayType.ALIPAY.getType();
    }
}
