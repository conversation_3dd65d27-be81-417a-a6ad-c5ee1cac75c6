package cn.jiawen.payment.strategy.impl;

import cn.jiawen.payment.entity.PaymentInfo;
import cn.jiawen.payment.enums.PayType;
import cn.jiawen.payment.service.WxPayService;
import cn.jiawen.payment.strategy.PaymentInfoStrategy;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

/**
 * 微信支付信息处理策略
 */
@Slf4j
@Component
public class WechatPaymentInfoStrategy implements PaymentInfoStrategy {

    @Resource
    private WxPayService wxPayService;

    @Override
    public PaymentInfo createPaymentInfo(String orderNo, Map<String, Object> paymentData) {

        log.info("使用微信支付策略创建支付信息，订单号：{}", orderNo);
        
        PaymentInfo paymentInfo = new PaymentInfo();
        paymentInfo.setOrderNo(orderNo);
        paymentInfo.setPaymentType(PayType.WXPAY.getType());
        // 默认值，会被实际数据覆盖
        paymentInfo.setTradeType("NATIVE");
        paymentInfo.setTradeState("SUCCESS");
        
        // 微信支付特有字段处理
        if (paymentData.containsKey("transaction_id")) {
            paymentInfo.setTransactionId((String) paymentData.get("transaction_id"));
        }

        // 处理交易类型
        if (paymentData.containsKey("trade_type")) {
            paymentInfo.setTradeType((String) paymentData.get("trade_type"));
        }

        // 处理交易状态
        if (paymentData.containsKey("trade_state")) {
            paymentInfo.setTradeState((String) paymentData.get("trade_state"));
        }

        // 处理支付金额（用户实际支付金额，单位：分）
        if (paymentData.containsKey("amount")) {
            Map<String, Object> amountMap = (Map<String, Object>) paymentData.get("amount");
            if (amountMap != null && amountMap.containsKey("payer_total")) {
                Object payerTotalObj = amountMap.get("payer_total");
                if (payerTotalObj instanceof Double) {
                    int payerTotal = ((Double) payerTotalObj).intValue();
                    paymentInfo.setPayerTotal(payerTotal);
                } else if (payerTotalObj instanceof Integer) {
                    paymentInfo.setPayerTotal((Integer) payerTotalObj);
                }
            }
        }
        
        // 设置支付内容（微信回调的完整数据）
        Gson gson = new Gson();
        String content = gson.toJson(paymentData);
        paymentInfo.setContent(content);
        
        paymentInfo.setCreateTime(new Date());
        paymentInfo.setUpdateTime(new Date());
        
        log.info("微信支付信息创建完成：{}", paymentInfo);
        return paymentInfo;
    }

    @Override
    public void processRefund(String orderNo, String reason) throws Exception {
        log.info("使用微信支付策略处理退款申请，订单号：{}，退款原因：{}", orderNo, reason);
        wxPayService.refund(orderNo, reason);
    }

    @Override
    public String getPaymentType() {
        return PayType.WXPAY.getType();
    }
}
