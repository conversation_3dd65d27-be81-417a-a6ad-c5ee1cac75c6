package cn.jiawen.payment.strategy;

import cn.jiawen.payment.entity.PaymentInfo;

import java.util.Map;

/**
 * 支付信息处理策略接口
 */
public interface PaymentInfoStrategy {

    /**
     * 创建支付信息
     * @param orderNo 订单号
     * @param paymentData 支付回调数据
     * @return PaymentInfo对象
     */
    PaymentInfo createPaymentInfo(String orderNo, Map<String, Object> paymentData);

    /**
     * 处理退款申请
     * @param orderNo 订单号
     * @param reason 退款原因
     * @throws Exception 退款处理异常
     */
    void processRefund(String orderNo, String reason) throws Exception;

    /**
     * 获取支付方式类型
     * @return 支付方式标识
     */
    String getPaymentType();
}
