package cn.jiawen.payment.task;

import cn.jiawen.payment.entity.OrderInfo;
import cn.jiawen.payment.entity.RefundInfo;
import cn.jiawen.payment.enums.PayType;
import cn.jiawen.payment.service.OrderInfoService;
import cn.jiawen.payment.service.RefundInfoService;
import cn.jiawen.payment.service.WxPayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class WxPayTask {

    @Resource
    private OrderInfoService orderInfoService;

    @Resource
    private WxPayService wxPayService;

    @Resource
    private RefundInfoService refundInfoService;

    /*
    * 秒 分 时 日 月 周
    * *: 每秒都执行
    * ？：不指定
    * 1-3: 从第1秒开始执行，到第3秒结束执行
    * 0/3: 从第0秒开始执行，每隔3秒执行1次
    * 1,2,3: 在指定的第1，2，3执行
    * 日和周不能同时指定，指定其中一个 则另一个为 ？
    * */
//    @Scheduled(cron = "0/3 * * * * ?")
//    public void task1() {
//        log.info("task1 被执行。。。。。。");
//    }

    /*
     * 从第0秒开始执行，每隔30秒执行1次, 查询创建超过5分钟并且未支付的订单
     * */
    @Scheduled(cron = "0/30 * * * * ?")
    public void orderConfirm() throws Exception {
        log.info("wxOrderConfirm 被执行。。。。。。");

        List<OrderInfo> orderInfoList = orderInfoService.getNoPayOrderByDuration(5, PayType.WXPAY.getType());

        for (OrderInfo orderInfo : orderInfoList) {
            String orderNo = orderInfo.getOrderNo();
            log.warn("超时订单 === {}", orderNo);

            // 核实订单状态：调用微信支付查单接口
            wxPayService.checkOrderStatus(orderNo);

        }
    }

    /**
     * 从第0秒开始每隔30秒执行1次，查询创建超过5分钟，并且未成功的退款单
     */
    @Scheduled(cron = "0/30 * * * * ?")
    public void refundConfirm() throws Exception {
        log.info("wxRefundConfirm 被执行......");

        //找出申请退款超过5分钟并且未成功的退款单
        List<RefundInfo> refundInfoList = refundInfoService.getNoRefundOrderByDuration(1);

        for (RefundInfo refundInfo : refundInfoList) {
            String refundNo = refundInfo.getRefundNo();
            log.warn("超时未退款的退款单号 ===> {}", refundNo);

            //核实订单状态：调用微信支付查询退款接口
            wxPayService.checkRefundStatus(refundNo);
        }
    }
}
