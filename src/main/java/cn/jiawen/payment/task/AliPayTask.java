package cn.jiawen.payment.task;

import cn.jiawen.payment.entity.OrderInfo;
import cn.jiawen.payment.enums.PayType;
import cn.jiawen.payment.service.AliPayService;
import cn.jiawen.payment.service.OrderInfoService;
import cn.jiawen.payment.service.RefundInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class AliPayTask {

    @Resource
    private OrderInfoService orderInfoService;

    @Resource
    private AliPayService aliPayService;

    /*
     * 从第0秒开始执行，每隔30秒执行1次, 查询创建超过5分钟并且未支付的订单
     * */
    @Scheduled(cron = "0/30 * * * * ?")
    public void orderConfirm() throws Exception {
        log.info("aliOrderConfirm 被执行。。。。。。");

        List<OrderInfo> orderInfoList = orderInfoService.getNoPayOrderByDuration(5, PayType.ALIPAY.getType());

        for (OrderInfo orderInfo : orderInfoList) {
            String orderNo = orderInfo.getOrderNo();
            log.warn("超时订单 ===> {}", orderNo);

            // 核实订单状态：调用支付宝查单接口
            aliPayService.checkOrderStatus(orderNo);

        }
    }
}
