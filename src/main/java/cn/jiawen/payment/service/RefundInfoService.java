package cn.jiawen.payment.service;

import cn.jiawen.payment.entity.RefundInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

public interface RefundInfoService extends IService<RefundInfo> {

    RefundInfo createRefundByOrderNo(String orderNo, String reason);

    void updateRefund(String content);

    /**
     * 使用策略模式更新退款信息
     * @param refundNo 退款单号
     * @param paymentType 支付类型
     * @param refundData 退款回调数据
     * @return 更新后的退款信息
     */
    RefundInfo updateRefundInfo(String refundNo, String paymentType, Map<String, Object> refundData);

    List<RefundInfo> getNoRefundOrderByDuration(int minutes);
}
