package cn.jiawen.payment.service.impl;

import cn.jiawen.payment.entity.PaymentInfo;
import cn.jiawen.payment.enums.PayType;
import cn.jiawen.payment.mapper.PaymentInfoMapper;
import cn.jiawen.payment.service.PaymentInfoService;
import cn.jiawen.payment.strategy.PaymentInfoStrategy;
import cn.jiawen.payment.strategy.PaymentInfoStrategyFactory;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class PaymentInfoServiceImpl implements PaymentInfoService {

    @Resource
    private PaymentInfoMapper paymentInfoMapper;

    @Resource
    private PaymentInfoStrategyFactory strategyFactory;

    /**
     * 创建支付信息（统一策略模式）
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createPaymentInfo(String orderNo, String paymentType, Map<String, Object> paymentData) {
        log.info("记录支付日志，订单号：{}，支付类型：{}", orderNo, paymentType);

        try {
            // 获取对应的策略
            PaymentInfoStrategy strategy = strategyFactory.getStrategy(paymentType);

            // 使用策略创建支付信息
            PaymentInfo paymentInfo = strategy.createPaymentInfo(orderNo, paymentData);

            // 保存到数据库
            paymentInfoMapper.insert(paymentInfo);

            log.info("支付信息记录成功，订单号：{}", orderNo);
        } catch (Exception e) {
            log.error("记录支付信息失败，订单号：{}，支付类型：{}", orderNo, paymentType, e);
            throw new RuntimeException("记录支付信息失败", e);
        }
    }
}
