package cn.jiawen.payment.service.impl;

import cn.jiawen.payment.config.WxPayConfig;
import cn.jiawen.payment.entity.OrderInfo;
import cn.jiawen.payment.entity.RefundInfo;
import cn.jiawen.payment.enums.OrderStatus;
import cn.jiawen.payment.enums.PayType;
import cn.jiawen.payment.enums.wxpay.WxApiType;
import cn.jiawen.payment.enums.wxpay.WxNotifyType;
import cn.jiawen.payment.enums.wxpay.WxRefundStatus;
import cn.jiawen.payment.enums.wxpay.WxTradeState;
import cn.jiawen.payment.service.OrderInfoService;
import cn.jiawen.payment.service.PaymentInfoService;
import cn.jiawen.payment.service.RefundInfoService;
import cn.jiawen.payment.service.WxPayService;
import cn.jiawen.payment.util.ApplicationNoUtils;
import cn.jiawen.payment.util.ImageUtils;
import com.google.gson.Gson;
import com.wechat.pay.contrib.apache.httpclient.util.AesUtil;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import vo.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;

@Service
public class WxPayServiceImpl implements WxPayService {

    @Resource
    private WxPayConfig wxPayConfig;

    @Resource
    @Qualifier("getWxPayClient")
    private CloseableHttpClient wxPayClient;

    @Resource
    @Qualifier("wxPayPublicKeyId")
    private String wxPayPublicKeyId;

    @Resource
    private OrderInfoService orderInfoService;

    @Resource
    private PaymentInfoService paymentInfoService;

    @Resource
    private RefundInfoService refundInfoService;

    @Resource
    @Qualifier("wxPayNoSignClient")
    private CloseableHttpClient wxPayNoSignClient; //无需应答签名

    private static final Logger log = LoggerFactory.getLogger(WxPayServiceImpl.class);

    private final ReentrantLock lock = new ReentrantLock();


    /**
     * 创建订单，调用Native支付接口
     *
     * @param productId 产品id
     * @return code_url 和 订单号
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, Object> nativePay(Long productId) throws Exception {

        // 生成订单
        OrderInfo orderInfo = orderInfoService.createOrderByProductId(productId, PayType.WXPAY.getType());


        //调用统一下单API
        HttpPost httpPost = new HttpPost(wxPayConfig.getDomain().concat(WxApiType.NATIVE_PAY.getType()));
        String codeUrl = orderInfo.getCodeUrl();
        if (orderInfo != null && !StringUtils.isEmpty(codeUrl)) {
            log.info("订单已存在，二维码已保存");

            // 返回二维码
            Map<String, Object> map = new HashMap<>();
            map.put("codeUrl", codeUrl);
            map.put("orderNo", orderInfo.getOrderNo());

            return map;
        }

        // 请求body参数
        Gson gson = new Gson();
        Map paramsMap = new HashMap();
        paramsMap.put("sp_appid", wxPayConfig.getSpAppid());
        paramsMap.put("sp_mchid", wxPayConfig.getSpMchid());
        paramsMap.put("description", orderInfo.getTitle());
        paramsMap.put("out_trade_no", orderInfo.getOrderNo());
        paramsMap.put("notify_url", wxPayConfig.getNotifyDomain().concat(WxNotifyType.NATIVE_NOTIFY.getType()));  // 回调地址

        Map amountMap = new HashMap();
        amountMap.put("total", orderInfo.getTotalFee());
        amountMap.put("currency", "CNY");

        paramsMap.put("amount", amountMap);

        //将参数转换成json字符串
        String jsonParams = gson.toJson(paramsMap);
        log.info("请求参数 ===> {}" + jsonParams);

        StringEntity entity = new StringEntity(jsonParams, "utf-8");
        entity.setContentType("application/json");
        httpPost.setEntity(entity);
        httpPost.setHeader("Accept", "application/json");
        httpPost.setHeader("Wechatpay-Serial", wxPayPublicKeyId);

        //完成签名并执行请求
        CloseableHttpResponse response = wxPayClient.execute(httpPost);

        try {
            String bodyAsString = EntityUtils.toString(response.getEntity()); // 响应体
            int statusCode = response.getStatusLine().getStatusCode(); // 响应状态码
            if (statusCode == 200) { // 处理成功
                log.info("成功, 返回结果 = " + bodyAsString);
            } else if (statusCode == 204) { // 处理成功，无返回Body
                log.info("成功");
            } else {
                log.info("Native下单失败,响应码 = " + statusCode + ",返回结果 = " + bodyAsString);
                throw new IOException("request failed");
            }

            // 响应结果
            Map<String, String> resultMap = gson.fromJson(bodyAsString, HashMap.class);
            // 解析二维码链接
            codeUrl = resultMap.get("code_url");

            // 保存二维码
            String orderNo = orderInfo.getOrderNo();
            orderInfoService.saveCodeUrl(orderNo, codeUrl);

            // 返回二维码
            Map<String, Object> map = new HashMap<>();
            map.put("codeUrl", codeUrl);
            map.put("orderNo", orderInfo.getOrderNo());

            return map;

        } finally {
            response.close();
        }

    }

    /**
     * 处理订单
     *
     * @param bodyMap 回调报文
     * @throws GeneralSecurityException 解密异常
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void processOrder(HashMap<String, Object> bodyMap) throws GeneralSecurityException {

        // 对称解密
        String plaintext = decryptFromResource(bodyMap);

        // 转换明文
        Gson gson = new Gson();
        HashMap plaintextMap = gson.fromJson(plaintext, HashMap.class);
        String orderNo = (String) plaintextMap.get("out_trade_no");

        // 并发处理（防止函数重入导致数据混乱）
        if (lock.tryLock()){  // 成功获取则立即返回true，获取失败则立即返回false。不必一直等待锁的释放
            try {
                // 接口幂等处理（防止应答超时导致生成重复支付日志）
                String orderStatus = orderInfoService.getOrderStatus(orderNo);
                if (!OrderStatus.NOTPAY.getType().equals(orderStatus)) {
                    return;
                }

                // 更新订单状态
                orderInfoService.updateStatusByOrderNo(orderNo, OrderStatus.SUCCESS);

                // 记录支付日志
                paymentInfoService.createPaymentInfo(orderNo, PayType.WXPAY.getType(), plaintextMap);

            } finally {
                // 主动释放锁
                lock.unlock();
            }
        }
    }

    /**
     * 用户取消订单
     * @param orderNo 订单编号
     */
    @Override
    public void cancelOrder(String orderNo) throws Exception {

        // 调用微信支付关闭订单接口
        this.closeOrder(orderNo);

        // 更新商户端的订单状态
        orderInfoService.updateStatusByOrderNo(orderNo, OrderStatus.CANCEL);
    }

    /**
     * 查询订单
     * @param orderNo 订单编号
     * @return String
     */
    @Override
    public String queryOrder(String orderNo) throws Exception {

        log.info("查询接口调用 ===> {}", orderNo);

        String url = String.format(WxApiType.ORDER_QUERY_BY_NO.getType(), orderNo);
        url = wxPayConfig.getDomain().concat(url).concat("?mchid=").concat(wxPayConfig.getSpMchid());

        HttpGet httpGet = new HttpGet(url);
        httpGet.setHeader("Accept", "application/json");
        httpGet.setHeader("Wechatpay-Serial", wxPayPublicKeyId);

        //完成签名并执行请求
        CloseableHttpResponse response = wxPayClient.execute(httpGet);

        try {
            String bodyAsString = EntityUtils.toString(response.getEntity()); // 响应体
            int statusCode = response.getStatusLine().getStatusCode(); // 响应状态码
            if (statusCode == 200) { // 处理成功
                log.info("成功, 返回结果 = " + bodyAsString);
            } else if (statusCode == 204) { // 处理成功，无返回Body
                log.info("成功");
            } else {
                log.info("Native下单失败,响应码 = " + statusCode + ",返回结果 = " + bodyAsString);
                throw new IOException("request failed");
            }

            return bodyAsString;

        } finally {
            response.close();
        }

    }

    /**
     * 根据订单号查询微信支付查单接口，核实订单状态
     * 如果订单已支付，则更新商户端订单状态
     * 如果订单未支付，则调用关单接口关闭订单，并更新商户端订单状态
     * @param orderNo 订单编号
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void checkOrderStatus(String orderNo) throws Exception {

        log.info("微信支付核实订单状态 ===> {}", orderNo);

        // 调用微信支付查单接口
        String res = this.queryOrder(orderNo);

        Gson gson = new Gson();
        HashMap resMap = gson.fromJson(res, HashMap.class);

        // 获取微信支付端的订单状态
        Object tradeState = resMap.get("trade_state");

        // 判断订单状态
        if (WxTradeState.SUCCESS.getType().equals(tradeState)) {

            log.warn("核实订单已支付 ===> {}", orderNo);

            // 订单已支付, 更新本地订单状态
            orderInfoService.updateStatusByOrderNo(orderNo, OrderStatus.SUCCESS);

            // 记录支付日志
            paymentInfoService.createPaymentInfo(orderNo, PayType.WXPAY.getType(), resMap);
        }

        if (WxTradeState.NOTPAY.getType().equals(tradeState)) {

            log.warn("核实订单未支付 ===> {}", orderNo);

            // 订单未支付 调用关单接口关闭订单，并更新商户端订单状态
            this.closeOrder(orderNo);
            orderInfoService.updateStatusByOrderNo(orderNo, OrderStatus.CLOSED);
        }

    }

    /**
     * 退款
     * @param orderNo 订单号
     * @param reason 退款原因
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void refund(String orderNo, String reason) throws Exception {

        log.info("创建退款单记录");
        // 根据订单编号创建退款单
        RefundInfo refundInfo = refundInfoService.createRefundByOrderNo(orderNo, reason);

        log.info("调用退款API");
        String url = wxPayConfig.getDomain().concat(WxApiType.DOMESTIC_REFUNDS.getType());
        HttpPost httpPost = new HttpPost(url);

        // 请求body参数
        Gson gson = new Gson();
        Map paramsMap = new HashMap();
        paramsMap.put("out_trade_no", orderNo); // 订单编号
        paramsMap.put("out_refund_no", refundInfo.getRefundNo());  // 退款单编号
        paramsMap.put("reason", reason); // 退款理由
        paramsMap.put("notify_url", wxPayConfig.getNotifyDomain().concat(WxNotifyType.REFUND_NOTIFY.getType()));  // 退款回调地址

        Map amountMap = new HashMap<>();
        amountMap.put("refund", refundInfo.getRefund()); // 退款金额
        amountMap.put("total", refundInfo.getTotalFee()); // 退款金额
        amountMap.put("currency", "CNY"); // 退款币种
        paramsMap.put("amount", amountMap); // 退款金额

        //将参数转换成json字符串
        String jsonParams = gson.toJson(paramsMap);
        log.info("请求参数 ===> {}" + jsonParams);

        StringEntity entity = new StringEntity(jsonParams, "utf-8");
        entity.setContentType("application/json");
        httpPost.setEntity(entity);
        httpPost.setHeader("Accept", "application/json");
        httpPost.setHeader("Wechatpay-Serial", wxPayPublicKeyId);

        //完成签名并执行请求
        CloseableHttpResponse response = wxPayClient.execute(httpPost);

        try {
            // 解析响应
            String bodyAsString = EntityUtils.toString(response.getEntity()); // 响应体
            int statusCode = response.getStatusLine().getStatusCode(); // 响应状态码
            if (statusCode == 200) { // 处理成功
                log.info("成功, 退款返回结果 = " + bodyAsString);
            } else if (statusCode == 204) { // 处理成功，无返回Body
                log.info("成功");
            } else {
                throw new RuntimeException("退款异常,响应码 = " + statusCode + ",返回结果 = " + bodyAsString);
            }

            // 更新订单状态
            orderInfoService.updateStatusByOrderNo(orderNo, OrderStatus.REFUND_PROCESSING);

            // 更新退款单
            refundInfoService.updateRefund(bodyAsString);

        } finally {
            response.close();
        }

    }

    /**
     * 查询退款接口调用
     * @param refundNo 退款单号
     * @return String
     */
    @Override
    public String queryRefund(String refundNo) throws Exception {

        log.info("查询退款接口调用 ===> {}", refundNo);

        String url =  String.format(WxApiType.DOMESTIC_REFUNDS_QUERY.getType(), refundNo);
        url = wxPayConfig.getDomain().concat(url);

        //创建远程Get 请求对象
        HttpGet httpGet = new HttpGet(url);
        httpGet.setHeader("Accept", "application/json");
        httpGet.setHeader("Wechatpay-Serial", wxPayPublicKeyId);

        //完成签名并执行请求
        CloseableHttpResponse response = wxPayClient.execute(httpGet);

        try {
            String bodyAsString = EntityUtils.toString(response.getEntity());
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                log.info("成功, 查询退款返回结果 = " + bodyAsString);
            } else if (statusCode == 204) {
                log.info("成功");
            } else {
                throw new RuntimeException("查询退款异常, 响应码 = " + statusCode+ ", 查询退款返回结果 = " + bodyAsString);
            }

            return bodyAsString;

        } finally {
            response.close();
        }

    }

    /**
     * 处理退款单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void processRefund(Map<String, Object> bodyMap) throws Exception {

        log.info("退款单");

        //解密报文
        String plainText = decryptFromResource(bodyMap);

        //将明文转换成map
        Gson gson = new Gson();
        HashMap plainTextMap = gson.fromJson(plainText, HashMap.class);
        String orderNo = (String)plainTextMap.get("out_trade_no");

        if(lock.tryLock()){
            try {

                String orderStatus = orderInfoService.getOrderStatus(orderNo);
                if (!OrderStatus.REFUND_PROCESSING.getType().equals(orderStatus)) {
                    return;
                }

                //更新订单状态
                orderInfoService.updateStatusByOrderNo(orderNo, OrderStatus.REFUND_SUCCESS);

                //更新退款单
                refundInfoService.updateRefund(plainText);

            } finally {
                //主动释放锁
                lock.unlock();
            }
        }
    }

    /**
     * 根据退款单号核实退款单状态
     * @param refundNo 退款单号
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void checkRefundStatus(String refundNo) throws Exception {

        log.warn("根据退款单号核实退款单状态 ===> {}", refundNo);

        //调用查询退款单接口
        String result = this.queryRefund(refundNo);

        //组装json请求体字符串
        Gson gson = new Gson();
        Map<String, String> resultMap = gson.fromJson(result, HashMap.class);

        //获取微信支付端退款状态
        String status = resultMap.get("status");

        String orderNo = resultMap.get("out_trade_no");

        if (WxRefundStatus.SUCCESS.getType().equals(status)) {

            log.warn("核实订单已退款成功 ===> {}", refundNo);

            //如果确认退款成功，则更新订单状态
            orderInfoService.updateStatusByOrderNo(orderNo, OrderStatus.REFUND_SUCCESS);

            //更新退款单
            refundInfoService.updateRefund(result);
        }

        if (WxRefundStatus.ABNORMAL.getType().equals(status)) {

            log.warn("核实订单退款异常  ===> {}", refundNo);

            //如果确认退款成功，则更新订单状态
            orderInfoService.updateStatusByOrderNo(orderNo, OrderStatus.REFUND_ABNORMAL);

            //更新退款单
            refundInfoService.updateRefund(result);
        }
    }

    /**
     * 申请账单
     * @param billDate 账单日期
     * @param type 账单类型（交易/资金）
     * @return url 下载地址
     * @throws Exception Exception
     */
    @Override
    public String queryBill(String billDate, String type) throws Exception {
        log.warn("申请账单接口调用 {}", billDate);

        String url = "";
        if("tradebill".equals(type)){
            url =  WxApiType.TRADE_BILLS.getType();
        }else if("fundflowbill".equals(type)){
            url =  WxApiType.FUND_FLOW_BILLS.getType();
        }else{
            throw new RuntimeException("不支持的账单类型");
        }

        url = wxPayConfig.getDomain().concat(url).concat("?bill_date=").concat(billDate);

        //创建远程Get 请求对象
        HttpGet httpGet = new HttpGet(url);
        httpGet.addHeader("Accept", "application/json");
        httpGet.addHeader("Wechatpay-Serial", wxPayPublicKeyId);

        //使用wxPayClient发送请求得到响应
        CloseableHttpResponse response = wxPayClient.execute(httpGet);

        try {

            String bodyAsString = EntityUtils.toString(response.getEntity());

            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                log.info("成功, 申请账单返回结果 = " + bodyAsString);
            } else if (statusCode == 204) {
                log.info("成功");
            } else {
                throw new RuntimeException("申请账单异常, 响应码 = " + statusCode+ ", 申请账单返回结果 = " + bodyAsString);
            }

            //获取账单下载地址
            Gson gson = new Gson();
            Map<String, String> resultMap = gson.fromJson(bodyAsString, HashMap.class);
            return resultMap.get("download_url");

        } finally {
            response.close();
        }
    }

    /**
     * 下载账单
     * @param billDate 账单日期
     * @param type 账单类型（交易/资金）
     * @return String
     * @throws Exception Exception
     */
    @Override
    public String downloadBill(String billDate, String type) throws Exception {
        log.warn("下载账单接口调用 {}, {}", billDate, type);

        //获取账单url地址
        String downloadUrl = this.queryBill(billDate, type);
        //创建远程Get 请求对象
        HttpGet httpGet = new HttpGet(downloadUrl);
        httpGet.addHeader("Accept", "application/json");

        CloseableHttpResponse response = wxPayNoSignClient.execute(httpGet);

        try {

            String bodyAsString = EntityUtils.toString(response.getEntity());

            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                log.info("成功, 下载账单返回结果 = " + bodyAsString);

            } else if (statusCode == 204) {
                log.info("成功");
            } else {
                throw new RuntimeException("下载账单异常, 响应码 = " + statusCode+ ", 下载账单返回结果 = " + bodyAsString);
            }

            return bodyAsString;

        } finally {
            response.close();
        }
    }

    /**
     * 图片上传至微信服务器
     * @param file 图片数据
     * @return String 微信返回的媒体文件标识ID。
     */
    @Override
    public String mediaUpload(MultipartFile file) throws Exception {

        // 创建远程请求对象
        String url = wxPayConfig.getDomain().concat(WxApiType.MEDIA_UPLOAD.getType());
        HttpPost httpPost = new HttpPost(url);

        // 组装json请求体
        Gson gson = new Gson();
        HashMap<Object, Object> paramsMap = new HashMap<>();

        // 媒体图片二进制内容
        paramsMap.put("file", file.getBytes());

        HashMap<Object, Object> metaMap = new HashMap<>();
        metaMap.put("filename", file.getOriginalFilename());
        metaMap.put("sha256", ImageUtils.calculateSha256(file.getBytes())); // 对图片文件的二进制内容进行sha256计算得到的值

        paramsMap.put("meta", metaMap);
        String jsonParams = gson.toJson(paramsMap);

        StringEntity entity = new StringEntity(jsonParams, "utf-8");
        entity.setContentType("multipart/form-data");
        httpPost.setEntity(entity);
        httpPost.setHeader("Accept", "application/json");

        //完成签名并执行请求
        CloseableHttpResponse response = wxPayClient.execute(httpPost);

        try {

            int statusCode = response.getStatusLine().getStatusCode(); // 响应状态码
            if (statusCode != 200) { // 处理成功
                log.info("图片上传失败,响应码 {} ", statusCode);
                throw new IOException("request failed");
            }

            log.info("图片上传成功");
            return response.toString();

        } finally {
            response.close();
        }

    }

    /**
     * 商户申请入驻
     * @param body 表单参数
     * @return String 微信支付分配的申请单号
     * @throws Exception Exception
     */
    @Override
    public String applyment(Map<String, String> body) throws Exception {

        // TODO: body参数需详细核对

        // 解密敏感数据
        Map<String, String> decryptedBody = decryptSensitiveData(body);

        // 创建远程请求对象
        String url = wxPayConfig.getDomain().concat(WxApiType.APPLYMENT_SUBMIT.getType());
        HttpPost httpPost = new HttpPost(url);

        // 构造申请单号
        String business_code = ApplicationNoUtils.generateApplicationNo();

        // 组装json请求体
        Gson gson = new Gson();
        HashMap<Object, Object> paramsMap = new HashMap<>();

        // 构建各个模块参数（使用解密后的数据）
        // 超级管理员信息
        HashMap<Object, Object> contactInfoMap = buildContactInfoMap(decryptedBody);
        // 主体资料
        HashMap<Object, Object> subjectInfoMap = buildSubjectInfoMap(decryptedBody);
        // 经营资料
        HashMap<Object, Object> businessInfoMap = buildBusinessInfoMap(decryptedBody);
        // 结算规则
        HashMap<Object, Object> settlementInfoMap = buildSettlementInfoMap();
        // 结算银行账户
        HashMap<Object, Object> bankAccountInfoMap = buildBankAccountInfoMap(decryptedBody);

        paramsMap.put("business_code", business_code);
        paramsMap.put("contact_info", contactInfoMap);
        paramsMap.put("subject_info", subjectInfoMap);
        paramsMap.put("business_info", businessInfoMap);
        paramsMap.put("settlement_info", settlementInfoMap);
        paramsMap.put("bank_account_info", bankAccountInfoMap);

        String jsonParams = gson.toJson(paramsMap);
        log.info("请求参数 ===> {}", jsonParams);
        StringEntity entity = new StringEntity(jsonParams, "utf-8");
        entity.setContentType("application/json");
        httpPost.setEntity(entity);
        httpPost.setHeader("Accept", "application/json");
        httpPost.setHeader("Wechatpay-Serial", wxPayPublicKeyId);  // 微信支付公钥加密请求参数

        // 完成签名并执行请求
        CloseableHttpResponse response = wxPayClient.execute(httpPost);

        try {

            int statusCode = response.getStatusLine().getStatusCode(); // 响应状态码
            if (statusCode != 200) {
                log.info("申请入驻失败，响应码 " + statusCode);
                throw new IOException("request failed");
            }
            log.info("申请入驻成功" );
            return response.toString();

        } finally {
            response.close();
        }
    }

    /**
     * 构建超级管理员信息
     */
    private HashMap<Object, Object> buildContactInfoMap(Map<String, String> body) {
        HashMap<Object, Object> contactInfoMap = new HashMap<>();
        contactInfoMap.put("contact_type", body.get("contact_type"));
        contactInfoMap.put("contact_name", body.get("contact_name"));
        contactInfoMap.put("mobile_phone", body.get("mobile_phone"));
        contactInfoMap.put("contact_email", body.get("contact_email"));

        // 超管类型分为法定代表人/经办人 经办人需要额外参数
        if ("SUPER".equals(body.get("contact_type"))) {
            contactInfoMap.put("contact_id_doc_type", body.get("contact_id_doc_type"));
            contactInfoMap.put("contact_id_number", body.get("contact_id_number"));
            contactInfoMap.put("contact_id_doc_copy", body.get("contact_id_doc_copy"));
            contactInfoMap.put("contact_id_doc_copy_back", body.get("contact_id_doc_copy_back"));
            contactInfoMap.put("contact_period_begin", body.get("contact_period_begin"));
            contactInfoMap.put("contact_period_end", body.get("contact_period_end"));
            contactInfoMap.put("business_authorization_letter", body.get("business_authorization_letter"));
        }

        return contactInfoMap;
    }

    /**
     * 构建主体资料
     */
    private HashMap<Object, Object> buildSubjectInfoMap(Map<String, String> body) {
        HashMap<Object, Object> subjectInfoMap = new HashMap<>();
        subjectInfoMap.put("subject_type", body.get("subject_type"));

        // 营业执照信息
        HashMap<Object, Object> businessLicenseInfoMap = new HashMap<>();
        businessLicenseInfoMap.put("license_copy", body.get("license_copy"));
        businessLicenseInfoMap.put("license_number", body.get("license_number"));
        businessLicenseInfoMap.put("merchant_name", body.get("merchant_name"));
        businessLicenseInfoMap.put("legal_person", body.get("legal_person"));
        businessLicenseInfoMap.put("license_address", body.get("license_address"));
        businessLicenseInfoMap.put("period_begin", body.get("period_begin"));
        businessLicenseInfoMap.put("period_end", body.get("period_end"));

        // 经营者/法定代表人身份证件身份证件信息
        HashMap<Object, Object> identityInfoMap = new HashMap<>();

        // 法定代表人说明函, 当证件持有人类型为经办人时，必须上传。其他情况，无需上传；
        if ("SUPER".equals(body.get("contact_type"))) {
            identityInfoMap.put("authorize_letter_copy", body.get("authorize_letter_copy"));
        }

        identityInfoMap.put("id_doc_type", body.get("id_doc_type"));

        HashMap<Object, Object> idCardInfoMap = new HashMap<>();
        idCardInfoMap.put("id_card_copy", body.get("id_card_copy"));
        idCardInfoMap.put("id_card_national", body.get("id_card_national"));
        idCardInfoMap.put("id_card_name", body.get("id_card_name"));


        if ("SUBJECT_TYPE_ENTERPRISE".equals(body.get("business_license_info"))) {
            // 身份证居住地址：主体类型为企业时，需要上传该参数。其他主体类型，无需上传；
            identityInfoMap.put("authorize_letter_copy", body.get("authorize_letter_copy"));

            // 最终受益人信息列表(UBO): 主体类型为企业时，按照下述要求填写
            HashMap<Object, Object> uboInfoListMap = new HashMap<>();
            uboInfoListMap.put("ubo_id_doc_type", body.get("ubo_id_doc_type"));
            uboInfoListMap.put("ubo_id_doc_copy", body.get("ubo_id_doc_copy"));
            uboInfoListMap.put("ubo_id_doc_copy_back", body.get("ubo_id_doc_copy_back"));
            uboInfoListMap.put("ubo_id_doc_name", body.get("ubo_id_doc_name"));
            uboInfoListMap.put("ubo_id_doc_number", body.get("ubo_id_doc_number"));
            uboInfoListMap.put("ubo_id_doc_address", body.get("ubo_id_doc_address"));
            uboInfoListMap.put("ubo_period_begin", body.get("ubo_period_begin"));
            uboInfoListMap.put("ubo_period_end", body.get("ubo_period_end"));

            idCardInfoMap.put("ubo_info_list", uboInfoListMap);
        }

        idCardInfoMap.put("id_card_number", body.get("id_card_number"));
        idCardInfoMap.put("card_period_begin", body.get("card_period_begin"));
        idCardInfoMap.put("card_period_end", body.get("card_period_end"));

        identityInfoMap.put("id_card_info", idCardInfoMap);
        subjectInfoMap.put("business_license_info", businessLicenseInfoMap);
        subjectInfoMap.put("identity_info", identityInfoMap);

        return subjectInfoMap;
    }

    /**
     * 构建经营资料
     */
    private HashMap<Object, Object> buildBusinessInfoMap(Map<String, String> body) {
        HashMap<Object, Object> businessInfoMap = new HashMap<>();
        businessInfoMap.put("merchant_shortname", body.get("merchant_shortname"));
        businessInfoMap.put("service_phone", body.get("service_phone"));

        // 销售场景信息
        HashMap<Object, Object> salesInfoMap = new HashMap<>();
        salesInfoMap.put("sales_scenes_type", body.get("sales_scenes_type"));

        // 小程序信息
        HashMap<Object, Object> miniProgramInfoMap = new HashMap<>();
        miniProgramInfoMap.put("mini_program_appid", wxPayConfig.getSpAppid());

        // 网站信息
        HashMap<Object, Object> webInfoMap = new HashMap<>();
        webInfoMap.put("domain", "https://pms.zkpms.com/");
        webInfoMap.put("web_authorisation", body.get("web_authorisation"));

        salesInfoMap.put("mini_program_info", miniProgramInfoMap);
        salesInfoMap.put("web_info", webInfoMap);
        businessInfoMap.put("sales_info", salesInfoMap);

        return businessInfoMap;
    }

    /**
     * 构建结算规则
     */
    private HashMap<Object, Object> buildSettlementInfoMap() {
        HashMap<Object, Object> settlementInfoMap = new HashMap<>();
        settlementInfoMap.put("settlement_id", "716");
        settlementInfoMap.put("qualification_type", "房产中介");
        return settlementInfoMap;
    }

    /**
     * 构建银行账户信息
     */
    private HashMap<Object, Object> buildBankAccountInfoMap(Map<String, String> body) {
        HashMap<Object, Object> bankAccountInfoMap = new HashMap<>();
        bankAccountInfoMap.put("bank_account_type", body.get("bank_account_type"));
        bankAccountInfoMap.put("account_name", body.get("account_bank"));
        bankAccountInfoMap.put("bank_branch_id", body.get("bank_branch_id"));
        bankAccountInfoMap.put("bank_name", body.get("bank_name"));
        bankAccountInfoMap.put("account_number", body.get("account_number"));
        return bankAccountInfoMap;
    }

    /**
     * 调用微信支付关闭订单接口
     * @param orderNo 订单编号
     */
    private void closeOrder(String orderNo) throws Exception {

        log.info("关闭订单接口调用，订单号 ===> {}", orderNo);

        // 创建远程请求对象
        String url = String.format(WxApiType.CLOSE_ORDER_BY_NO.getType(), orderNo);
        url = wxPayConfig.getDomain().concat(url);
        HttpPost httpPost = new HttpPost(url);

        // 组装json请求体
        Gson gson = new Gson();
        HashMap<Object, Object> paramsMap = new HashMap<>();
        paramsMap.put("mchid", wxPayConfig.getSpMchid());
        String jsonParams = gson.toJson(paramsMap);
        log.info("请求参数 ===> {}", jsonParams);

        StringEntity entity = new StringEntity(jsonParams, "utf-8");
        entity.setContentType("application/json");
        httpPost.setEntity(entity);
        httpPost.setHeader("Accept", "application/json");
        httpPost.setHeader("Wechatpay-Serial", wxPayPublicKeyId);

        //完成签名并执行请求
        CloseableHttpResponse response = wxPayClient.execute(httpPost);

        try {

            int statusCode = response.getStatusLine().getStatusCode(); // 响应状态码
            if (statusCode == 200) { // 处理成功
                log.info("成功");
            } else if (statusCode == 204) { // 处理成功，无返回Body
                log.info("成功");
            } else {
                log.info("Native下单失败,响应码 = " + statusCode);
                throw new IOException("request failed");
            }

        } finally {
            response.close();
        }
    }

    /**
     * 对称解密
     * @param bodyMap 通知数据
     * @return String 明文
     */
    private String decryptFromResource(Map<String, Object> bodyMap) throws GeneralSecurityException {

        // 通知数据
        Map<String, String> resourceMap = (Map) bodyMap.get("resource");
        // 数据密文
        String ciphertext = resourceMap.get("ciphertext");
        // 随机串
        String nonce = resourceMap.get("nonce");
        // 附加数据
        String associatedData = resourceMap.get("associated_data");

        log.info("密文 ===> {}", ciphertext);

        AesUtil aesUtil = new AesUtil(wxPayConfig.getApiV3Key().getBytes(StandardCharsets.UTF_8));
        String plainText = aesUtil.decryptToString(
                associatedData.getBytes(StandardCharsets.UTF_8),
                nonce.getBytes(StandardCharsets.UTF_8),
                ciphertext);

        log.info("明文 ===> {}", plainText);

        return plainText;
    }

    /**
     * 解密敏感数据
     * @param body 包含加密数据的请求体
     * @return 解密后的数据
     */
    private Map<String, String> decryptSensitiveData(Map<String, String> body) {
        // 定义需要解密的敏感字段
        String[] sensitiveFields = {
            "contact_name", "mobile_phone", "contact_email", "id_card_name", "id_card_number",
            "legal_person", "beneficiary_name", "beneficiary_id_number", "beneficiary_address",
            "account_name", "account_number", "agent_id_number", "agent_name"
        };

        Map<String, String> decryptedBody = new HashMap<>(body);

        for (String field : sensitiveFields) {
            String encryptedValue = body.get(field);
            if (encryptedValue != null && !encryptedValue.trim().isEmpty()) {
                try {
                    // 检查是否是Base64编码的加密数据
                    if (isBase64Encrypted(encryptedValue)) {
                        // 后端不需要解密，直接将加密数据发送给微信支付
                        log.info("字段 {} 已加密，长度: {} 字符", field, encryptedValue.length());
                        // 保持加密状态，直接传递给微信支付
                        decryptedBody.put(field, encryptedValue);
                    } else {
                        // 明文数据，需要在后端进行加密
                        String encryptedData = wxPayConfig.encryptSensitiveInfo(encryptedValue);
                        decryptedBody.put(field, encryptedData);
                        log.info("字段 {} 在后端进行了加密", field);
                    }
                } catch (Exception e) {
                    log.error("处理敏感字段 {} 时发生错误: {}", field, e.getMessage());
                    // 如果处理失败，尝试在后端加密
                    try {
                        String encryptedData = wxPayConfig.encryptSensitiveInfo(encryptedValue);
                        decryptedBody.put(field, encryptedData);
                        log.info("字段 {} 在后端进行了备用加密", field);
                    } catch (Exception ex) {
                        log.error("字段 {} 备用加密也失败: {}", field, ex.getMessage());
                        // 保持原值
                        decryptedBody.put(field, encryptedValue);
                    }
                }
            }
        }

        return decryptedBody;
    }

    /**
     * 简单判断字符串是否可能是Base64编码的加密数据
     * @param value 待判断的字符串
     * @return true表示可能是加密数据
     */
    private boolean isBase64Encrypted(String value) {
        // 简单的启发式判断：
        // 1. 长度较长（加密后的数据通常比较长）
        // 2. 包含Base64字符
        // 3. 长度符合RSA加密后的特征
        if (value == null || value.length() < 100) {
            return false;
        }

        // 检查是否只包含Base64字符
        return value.matches("^[A-Za-z0-9+/]*={0,2}$") && value.length() > 200;
    }

}
