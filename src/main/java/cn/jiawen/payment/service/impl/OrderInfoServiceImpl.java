package cn.jiawen.payment.service.impl;

import cn.jiawen.payment.entity.OrderInfo;
import cn.jiawen.payment.entity.Product;
import cn.jiawen.payment.enums.OrderStatus;
import cn.jiawen.payment.mapper.OrderInfoMapper;
import cn.jiawen.payment.mapper.ProductMapper;
import cn.jiawen.payment.service.OrderInfoService;
import cn.jiawen.payment.util.OrderNoUtils;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

@Slf4j
@Service
public class OrderInfoServiceImpl extends ServiceImpl<OrderInfoMapper, OrderInfo> implements OrderInfoService {

    @Resource
    private ProductMapper productMapper;

    @Resource
    private OrderInfoMapper orderInfoMapper;

    /**
     * @param productId 商品id
     * @return OrderInfo
     */
    @Override
    public OrderInfo createOrderByProductId(Long productId, String paymentType) {

        // 查找已存在却未支付的订单 --> ( 防止用户重复点击导致生成多次订单 )
        OrderInfo orderInfo = this.getNoPayOrderByProductId(productId, paymentType);
        if (orderInfo != null) {
            return orderInfo;
        }

        // 获取商品信息
        Product product = productMapper.selectById(productId);

        // 生成订单
        orderInfo = new OrderInfo();
        orderInfo.setTitle("test"); // TODO: 商品名 product.getTitle()
        orderInfo.setOrderNo(OrderNoUtils.getOrderNo());
        orderInfo.setProductId(productId);
        orderInfo.setTotalFee(1);  // todo: 单位：分 product.getPrice()
        orderInfo.setOrderStatus(OrderStatus.NOTPAY.getType());
        orderInfo.setPaymentType(paymentType);
        // TODO: 下单用户
//        orderInfo.setUserId()
        orderInfoMapper.insert(orderInfo);

        return orderInfo;
    }

    /**
     * 根据产品id查询未支付订单
     * @param productId 产品id
     * @return 订单
     */
    private OrderInfo getNoPayOrderByProductId(Long productId, String paymentType) {

        //TODO: 该段代码适用与mybatisPlus框架  若使用mybatis请修改
        QueryWrapper<OrderInfo> QueryWrapper = new QueryWrapper<>();
        QueryWrapper.eq("product_id", productId);
        QueryWrapper.eq("order_status", OrderStatus.NOTPAY.getType());
        QueryWrapper.eq("payment_type", paymentType);
        // TODO: 补全当前用户
//        QueryWrapper.eq("user_id", userId);
        OrderInfo orderInfo = orderInfoMapper.selectOne(QueryWrapper);

        return orderInfo;
    }

    /**
     * 存储订单二维码
     * @param orderNo 订单号
     * @param codeUrl 二维码链接
     */
    @Override
    public void saveCodeUrl(String orderNo, String codeUrl) {

        // TODO: 该段代码适用与mybatisPlus框架  若使用mybatis请修改
        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no", orderNo);

        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setCodeUrl(codeUrl);

        orderInfoMapper.update(orderInfo, queryWrapper);

    }

    /**
     * 倒序查询订单列表
     * @return 订单列表
     */
    @Override
    public List<OrderInfo> listOrderByCreateTimeDesc() {

        //TODO: 该段代码适用与mybatisPlus框架  若使用mybatis请修改
        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<OrderInfo>().orderByDesc("create_time");

        return orderInfoMapper.selectList(queryWrapper);
    }

    /**
     * 根据订单号更新订单状态
     * @param orderNo 订单号
     * @param orderStatus 订单状态
     */
    @Override
    public void updateStatusByOrderNo(String orderNo, OrderStatus orderStatus) {

        log.info("更新订单状态 ===> {}", orderStatus.getType());

        //TODO: 该段代码适用与mybatisPlus框架  若使用mybatis请修改
        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no", orderNo);

        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOrderStatus(orderStatus.getType());

        orderInfoMapper.update(orderInfo, queryWrapper);
    }

    /**
     * 根据订单号获取订单状态
     * @param orderNo 订单号
     * @return orderStatus 订单状态
     */
    @Override
    public String getOrderStatus(String orderNo) {

        //TODO: 该段代码适用与mybatisPlus框架  若使用mybatis请修改
        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no", orderNo);

        OrderInfo orderInfo = orderInfoMapper.selectOne(queryWrapper);
        if (orderInfo == null) {
            return null;
        }

        return orderInfo.getOrderStatus();
    }

    /**
     * 查询创建超过 minutes分钟并且未支付的订单
     * @param minutes 超时时间
     * @return List<OrderInfo> 订单列表
     */
    @Override
    public List<OrderInfo> getNoPayOrderByDuration(int minutes, String payment) {

        Instant instant = Instant.now().minus(Duration.ofMinutes(minutes));

        // TODO: 该段代码适用与mybatisPlus框架  若使用mybatis请修改
        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_status", OrderStatus.NOTPAY.getType());
        queryWrapper.eq("payment_type", payment);
        queryWrapper.le("create_time", instant);

        return orderInfoMapper.selectList(queryWrapper);
    }

    /**
     * 根据订单号获取订单
     * @param orderNo 订单号
     * @return OrderInfo
     */
    @Override
    public OrderInfo getOrderByOrderNo(String orderNo) {
        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no", orderNo);

        return orderInfoMapper.selectOne(queryWrapper);
    }
}
