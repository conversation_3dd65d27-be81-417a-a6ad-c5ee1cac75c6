package cn.jiawen.payment.service.impl;

import cn.jiawen.payment.entity.OrderInfo;
import cn.jiawen.payment.entity.RefundInfo;
import cn.jiawen.payment.enums.OrderStatus;
import cn.jiawen.payment.enums.PayType;
import cn.jiawen.payment.enums.wxpay.AliTradeState;
import cn.jiawen.payment.service.AliPayService;
import cn.jiawen.payment.service.OrderInfoService;
import cn.jiawen.payment.service.PaymentInfoService;
import cn.jiawen.payment.service.RefundInfoService;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.request.*;
import com.alipay.api.response.*;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Service
public class AliPayServiceImpl implements AliPayService {

    @Resource
    private OrderInfoService orderInfoService;

    @Resource
    private RefundInfoService refundInfoService;

    @Resource
    private PaymentInfoService paymentInfoService;

    @Resource
    private AlipayClient alipayClient;

    @Resource
    private Environment config;

    private final ReentrantLock lock = new ReentrantLock();

    /**
     * 统一收单下单并支付页面
     * @param productId 订单号
     * @return form字符串
     */
    @Transactional
    @Override
    public String tradeCreate(Long productId){

        try {
            // 生成订单
            OrderInfo orderInfo = orderInfoService.createOrderByProductId(productId, PayType.ALIPAY.getType());
            log.info("订单已生成，订单号编号为 ===> {}", orderInfo.getOrderNo());

            // 构造请求参数以调用支付接口https://opendocs.alipay.com/open/59da99d0_alipay.trade.page.pay?scene=22&pathHash=e26b497f
            AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();
            // 组装公共请求参数
            request.setReturnUrl(config.getProperty("alipay.return-url"));  // 支付成功页面跳转地址
            request.setNotifyUrl(config.getProperty("alipay.notify-url"));  // 回调地址

            // 组装业务扩展参数
            JSONObject bizContent = new JSONObject();
            bizContent.put("out_trade_no", orderInfo.getOrderNo());
            // TODO: 金额处理 orderInfo.getTotalFee() 单位：元
            BigDecimal total = new BigDecimal(orderInfo.getTotalFee().toString()).divide(new BigDecimal(100));
            bizContent.put("total_amount", total);
            bizContent.put("subject", "test");  // TODO: 商品名称 orderInfo.getTitle()
            bizContent.put("product_code", "FAST_INSTANT_TRADE_PAY");
            // TODO: 可选参数 根据业务需求选择是否传入
            // 第三方代调用模式下请设置app_auth_token
            // request.putOtherTextParam("app_auth_token", "<-- 请填写应用授权令牌 -->");


            request.setBizContent(bizContent.toString());
            // 执行请求 调用支付宝接口
            AlipayTradePagePayResponse response = alipayClient.pageExecute(request, "POST");

            if (response.isSuccess()) {
                log.info("调用成功,返回结果 ===> {}", response.getBody());
                return response.getBody();
            } else {
                log.info("调用失败,返回码 ===> " + response.getCode() + ", 返回描述 ===> " + response.getMsg());
                throw new RuntimeException("创建支付交易失败");
            }

        } catch (AlipayApiException e) {
            throw new RuntimeException("创建支付交易失败", e);
        }
    }

    /**
     * 处理订单
     * @param params
     */
    @Override
    public void processOrder(Map<String, String> params) {

        log.info("处理订单");

        // 获取订单号
        String orderNo = params.get("out_trade_no");

        if (lock.tryLock()) {

            try {
                // 处理重复通知：幂等性处理
                String orderStatus = orderInfoService.getOrderStatus(orderNo);
                if (!OrderStatus.NOTPAY.getType().equals(orderStatus)) {
                    return;
                }

                // 更新订单状态
                orderInfoService.updateStatusByOrderNo(orderNo, OrderStatus.SUCCESS);

                // 记录支付日志
                Map<String, Object> paymentData = new HashMap<>();
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    paymentData.put(entry.getKey(), entry.getValue());
                }
                paymentInfoService.createPaymentInfo(orderNo, PayType.ALIPAY.getType(), paymentData);
            } finally {
                lock.unlock();
            }
        }
    }

    /**
     * 用户取消订单
     * @param orderNo 订单号
     */
    @Override
    public void cancelOrder(String orderNo) {

        // 调用支付宝提供的统一收单交易关闭接口
        this.closeOrder(orderNo);

        // 更新用户的订单状态
        orderInfoService.updateStatusByOrderNo(orderNo, OrderStatus.CANCEL);

    }

    /**
     * 支付宝关单接口
     * @param orderNo 订单号
     */
    private void closeOrder(String orderNo) {

        try {
            log.info("关单接口调用，订单号 ===> {}", orderNo);

            // 构造请求参数以调用接口
            AlipayTradeCloseRequest request = new AlipayTradeCloseRequest();
            JSONObject bizContent = new JSONObject();

            // 设置订单支付时传入的商户订单号
            bizContent.put("out_trade_no", orderNo);
            request.setBizContent(bizContent.toString());
            // TODO: 第三方代调用模式下请设置app_auth_token
            // request.putOtherTextParam("app_auth_token", "<-- 请填写应用授权令牌 -->");

            AlipayTradeCloseResponse response = alipayClient.execute(request);

            if (response.isSuccess()) {
                log.info("调用成功,返回结果 ===> {}", response.getBody());
            } else {
                log.info("调用失败,返回码 ===> " + response.getCode() + ", 返回描述 ===> " + response.getMsg());
                throw new RuntimeException("关单接口调用失败");
            }
        } catch (AlipayApiException e) {
            throw new RuntimeException("关单接口调用失败", e);
        }

    }

    /**
     * 查询订单
     * @param orderNo 订单编号
     * @return String
     */
    @Override
    public String queryOrder(String orderNo){

        try {
            log.info("查询接口调用 ===> {}", orderNo);

            // 构造请求参数以调用接口
            AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();

            JSONObject bizContent = new JSONObject();
            bizContent.put("out_trade_no", orderNo);

            request.setBizContent(bizContent.toString());
            // 第三方代调用模式下请设置app_auth_token
            // request.putOtherTextParam("app_auth_token", "<-- 请填写应用授权令牌 -->");

            AlipayTradeQueryResponse response = alipayClient.execute(request);

            if (response.isSuccess()) {
                log.info("调用成功,返回结果 ===> {}", response.getBody());
                return response.getBody();
            } else {
                log.info("调用失败,返回码 ===> " + response.getCode() + ", 返回描述 ===> " + response.getMsg());
//                throw new RuntimeException("查单接口调用失败");
                return null; // 订单不存在
            }
        } catch (AlipayApiException e) {
            throw new RuntimeException("查询订单接口调用失败", e);
        }

    }

    /**
     * 根据订单号调用支付宝查单接口，核实订单状态
     * 果订单未创建，则更新商户端订单状态（关单）
     * 如果订单已支付，则更新商户端订单状态，记录支付日志
     * 如果订单未支付，则调用关单接口关闭订单，并更新商户端订单状态
     * @param orderNo 订单编号
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void checkOrderStatus(String orderNo) {

        log.info("支付宝核实订单状态 ===> {}", orderNo);

        // 调用支付宝查单接口
        String res = this.queryOrder(orderNo);

        // 订单未创建
        if (res == null) {
            log.warn("核实订单还未创建 ===> {}", orderNo);
            // 更新本地订单状态
            orderInfoService.updateStatusByOrderNo(orderNo, OrderStatus.CLOSED);
            return;
        }

        Gson gson = new Gson();
        HashMap<String, LinkedTreeMap> resMap = gson.fromJson(res, HashMap.class);

        // 获取支付宝端的订单状态
        LinkedTreeMap alipayTradeQueryResponse = resMap.get("alipay_trade_query_response");
        String tradeStatus = (String) alipayTradeQueryResponse.get("trade_status");

        // 判断订单状态
        if (AliTradeState.SUCCESS.getType().equals(tradeStatus)) {

            log.warn("核实订单已支付 ===> {}", orderNo);

            // 订单已支付, 更新本地订单状态
            orderInfoService.updateStatusByOrderNo(orderNo, OrderStatus.SUCCESS);

            // 记录支付日志
            paymentInfoService.createPaymentInfo(orderNo, PayType.ALIPAY.getType(), alipayTradeQueryResponse);
        }

        if (AliTradeState.NOTPAY.getType().equals(tradeStatus)) {

            log.warn("核实订单未支付 ===> {}", orderNo);

            // 订单未支付 调用关单接口关闭订单，并更新商户端订单状态
            this.closeOrder(orderNo);
            orderInfoService.updateStatusByOrderNo(orderNo, OrderStatus.CLOSED);
        }

    }

    /**
     * 退款
     * @param orderNo 订单号
     * @param reason 退款原因
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
//    https://opendocs.alipay.com/open/f60979b3_alipay.trade.refund?scene=common&pathHash=e4c921a7
    public void refund(String orderNo, String reason){

        try {
            log.info("支付宝创建退款单记录 ===> {}", orderNo);
            // 根据订单编号创建退款单
            RefundInfo refundInfo = refundInfoService.createRefundByOrderNo(orderNo, reason);

            // 构造请求参数以调用接口
            AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();

            // 组装业务请求参数
            JSONObject bizContent = new JSONObject();
            // 退款金额。需要退款的金额，该金额不能大于订单金额，单位为元，支持两位小数
            BigDecimal refund = new BigDecimal(refundInfo.getRefund().toString()).divide(new BigDecimal(100));            
            bizContent.put("refund_amount", refund);
            bizContent.put("out_trade_no", orderNo);
            bizContent.put("refund_reason", reason);

            // TODO: 可选参数 根据业务需求加入
            // 退款请求号 标识一次退款请求，需要保证在交易号下唯一， 如需部分退款，则此参数必传。
//        jsonObject.put("out_request_no", out_request_no);

            request.setBizContent(bizContent.toString());
            // 第三方代调用模式下请设置app_auth_token
            // request.putOtherTextParam("app_auth_token", "<-- 请填写应用授权令牌 -->");

            AlipayTradeRefundResponse response = alipayClient.execute(request);

            Gson gson = new Gson();
            Map<String, Object> responseMap = gson.fromJson(response.getBody(), HashMap.class);

            if (response.isSuccess()) {

                log.info("支付宝退款调用成功,返回结果 ===> {}", response.getBody());

                // 更新订单状态为退款成功（支付宝退款是同步的）
                orderInfoService.updateStatusByOrderNo(orderNo, OrderStatus.REFUND_SUCCESS);
                // 更新退款单状态 - 策略
                refundInfoService.updateRefundInfo(refundInfo.getRefundNo(), PayType.ALIPAY.getType(), responseMap);

            } else {

                log.warn("支付宝退款调用失败,返回码: {}, 返回描述: {}", response.getCode(), response.getMsg());

                //更新订单状态
                orderInfoService.updateStatusByOrderNo(orderNo, OrderStatus.REFUND_ABNORMAL);
                //更新退款单
                refundInfoService.updateRefundInfo(refundInfo.getRefundNo(), PayType.ALIPAY.getType(), responseMap);
            }

        } catch (AlipayApiException e) {
            log.error("支付宝退款API调用异常，订单号: {}", orderNo, e);
            throw new RuntimeException("创建退款申请失败");
        }
    }

    /**
     * 统一收单交易退款查询
     * @param orderNo 订单编号
     * @return String
     */
    @Override
    public String queryRefund(String orderNo) {

        try {
            log.info("查询退款接口调用 ===> {}", orderNo);

            AlipayTradeFastpayRefundQueryRequest request = new AlipayTradeFastpayRefundQueryRequest();
            JSONObject bizContent = new JSONObject();
            bizContent.put("out_trade_no", orderNo);
            bizContent.put("out_request_no", orderNo);
            request.setBizContent(bizContent.toString());

            AlipayTradeFastpayRefundQueryResponse response = alipayClient.execute(request);
            if(response.isSuccess()){
                log.info("调用成功，返回结果 ===> " + response.getBody());
                return response.getBody();
            } else {
                log.info("调用失败，返回码 ===> " + response.getCode() + ", 返回描述 ===> " + response.getMsg());
                //throw new RuntimeException("查单接口的调用失败");
                return null; // 订单不存在
            }

        } catch (AlipayApiException e) {
            e.printStackTrace();
            throw new RuntimeException("查单接口的调用失败");
        }
    }

    /**
     * 申请账单
     * @param billDate 申请日期
     * @param type 申请类型
     * @return url
     */
    @Override
    public String queryBill(String billDate, String type) {

        try {

            AlipayDataDataserviceBillDownloadurlQueryRequest request = new AlipayDataDataserviceBillDownloadurlQueryRequest();
            JSONObject bizContent = new JSONObject();
            bizContent.put("bill_type", type);
            bizContent.put("bill_date", billDate);
            request.setBizContent(bizContent.toString());
            AlipayDataDataserviceBillDownloadurlQueryResponse response = alipayClient.execute(request);

            if(response.isSuccess()){
                log.info("调用成功，返回结果 ===> {}", response.getBody());

                //获取账单下载地址
                Gson gson = new Gson();
                HashMap<String, LinkedTreeMap> resultMap = gson.fromJson(response.getBody(), HashMap.class);
                LinkedTreeMap billDownloadurlResponse = resultMap.get("alipay_data_dataservice_bill_downloadurl_query_response");
                String billDownloadUrl = (String)billDownloadurlResponse.get("bill_download_url");

                return billDownloadUrl;
            } else {
                log.info("调用失败，返回码 ===> " + response.getCode() + ", 返回描述 ===> " + response.getMsg());
                throw new RuntimeException("申请账单失败");
            }

        } catch (AlipayApiException e) {
            e.printStackTrace();
            throw new RuntimeException("申请账单失败");
        }
    }

}
