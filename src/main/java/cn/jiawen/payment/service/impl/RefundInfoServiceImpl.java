package cn.jiawen.payment.service.impl;

import cn.jiawen.payment.entity.OrderInfo;
import cn.jiawen.payment.entity.RefundInfo;
import cn.jiawen.payment.enums.PayType;
import cn.jiawen.payment.enums.wxpay.WxRefundStatus;
import cn.jiawen.payment.mapper.RefundInfoMapper;
import cn.jiawen.payment.service.OrderInfoService;
import cn.jiawen.payment.service.RefundInfoService;

import cn.jiawen.payment.util.OrderNoUtils;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;

@Slf4j
@Service
public class RefundInfoServiceImpl extends ServiceImpl<RefundInfoMapper, RefundInfo> implements RefundInfoService {

    @Resource
    private OrderInfoService orderInfoService;

    @Resource
    private RefundInfoMapper refundInfoMapper;

    /**
     * 根据订单号创建退款订单
     * @param orderNo 订单编号
     * @param reason 退款原因
     * @return 退款单
     */
    @Override
    public RefundInfo createRefundByOrderNo(String orderNo, String reason) {

        // 根据订单号获取订单信息
        OrderInfo orderInfo = orderInfoService.getOrderByOrderNo(orderNo);

        // 根据订单号生成退款订单
        RefundInfo refundInfo = new RefundInfo();
        refundInfo.setOrderNo(orderNo); // 订单编号
        refundInfo.setRefundNo(OrderNoUtils.getRefundNo()); // 退款单编号

        System.out.println(orderInfo.getTotalFee());

        refundInfo.setTotalFee(orderInfo.getTotalFee()); // 原订单金额（分）
        refundInfo.setRefund(orderInfo.getTotalFee());  // 退款金额（分）
        refundInfo.setReason(reason);   // 退款原因

        // 保存退款订单
        refundInfoMapper.insert(refundInfo);

        return refundInfo;
    }

    /**
     * 记录退款记录
     * @param content 记录
     */
    @Override
    public void updateRefund(String content) {

        //将json字符串转换成Map
        Gson gson = new Gson();
        Map<String, String> resultMap = gson.fromJson(content, HashMap.class);

        //根据退款单编号修改退款单
        QueryWrapper<RefundInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("refund_no", resultMap.get("out_refund_no"));

        //设置要修改的字段
        RefundInfo refundInfo = new RefundInfo();

        refundInfo.setRefundId(resultMap.get("refund_id")); // 微信支付退款单号

        //查询退款和申请退款中的返回参数
        if(resultMap.get("status") != null){
            refundInfo.setRefundStatus(resultMap.get("status"));//退款状态
            refundInfo.setContentReturn(content); // 将全部响应结果存入数据库的content字段
        }
        //退款回调中的回调参数
        if(resultMap.get("refund_status") != null){
            refundInfo.setRefundStatus(resultMap.get("refund_status"));//退款状态
            refundInfo.setContentNotify(content); // 将全部响应结果存入数据库的content字段
        }

        //更新退款单
        refundInfoMapper.update(refundInfo, queryWrapper);
    }

    /**
     * 使用策略模式更新退款信息
     * @param refundNo 退款单号
     * @param paymentType 支付类型
     * @param refundData 退款回调数据
     * @return 更新后的退款信息
     */
    @Override
    public RefundInfo updateRefundInfo(String refundNo, String paymentType, Map<String, Object> refundData) {

        log.info("更新退款信息，退款单号：{}，支付类型：{}", refundNo, paymentType);

        try {
            // 根据退款单号查询退款信息
            QueryWrapper<RefundInfo> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("refund_no", refundNo);
            RefundInfo refundInfo = refundInfoMapper.selectOne(queryWrapper);

            if (refundInfo == null) {
                throw new RuntimeException("退款单不存在：" + refundNo);
            }

            // 根据支付类型处理不同的退款数据
            if (PayType.WXPAY.getType().equals(paymentType)) {
                // 微信支付退款数据处理
                updateWechatRefundInfo(refundInfo, refundData);
            } else if (PayType.ALIPAY.getType().equals(paymentType)) {
                // 支付宝退款数据处理
                updateAlipayRefundInfo(refundInfo, refundData);
            } else {
                throw new RuntimeException("不支持的支付类型：" + paymentType);
            }

            // 更新退款单
            refundInfoMapper.update(refundInfo, queryWrapper);

            log.info("退款信息更新成功，退款单号：{}", refundNo);
            return refundInfo;

        } catch (Exception e) {
            log.error("更新退款信息失败，退款单号：{}，支付类型：{}", refundNo, paymentType, e);
            throw new RuntimeException("更新退款信息失败", e);
        }
    }

    /**
     * 找出申请退款超过minutes分钟并且未成功的退款单
     * @param minutes
     * @return
     */
    @Override
    public List<RefundInfo> getNoRefundOrderByDuration(int minutes) {

        // minutes分钟之前的时间
        Instant instant = Instant.now().minus(Duration.ofMinutes(minutes));

        QueryWrapper<RefundInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("refund_status", WxRefundStatus.PROCESSING.getType());
        queryWrapper.le("create_time", instant);
        List<RefundInfo> refundInfoList = baseMapper.selectList(queryWrapper);
        return refundInfoList;
    }

    /**
     * 更新微信退款信息
     */
    private void updateWechatRefundInfo(RefundInfo refundInfo, Map<String, Object> refundData) {
        // 微信退款ID
        if (refundData.containsKey("refund_id")) {
            refundInfo.setRefundId((String) refundData.get("refund_id"));
        }

        // 退款状态
        if (refundData.containsKey("status")) {
            refundInfo.setRefundStatus((String) refundData.get("status"));
        } else if (refundData.containsKey("refund_status")) {
            refundInfo.setRefundStatus((String) refundData.get("refund_status"));
        }

        // 保存完整的回调数据
        Gson gson = new Gson();
        String content = gson.toJson(refundData);
        if (refundData.containsKey("status")) {
            refundInfo.setContentReturn(content);
        } else {
            refundInfo.setContentNotify(content);
        }
    }

    /**
     * 更新支付宝退款信息
     */
    private void updateAlipayRefundInfo(RefundInfo refundInfo, Map<String, Object> refundData) {
        // 支付宝退款状态直接设置为成功（支付宝退款是同步的）
        refundInfo.setRefundStatus("SUCCESS");

        // 支付宝交易号
        if (refundData.containsKey("trade_no")) {
            refundInfo.setRefundId((String) refundData.get("trade_no"));
        }

        // 保存完整的响应数据
        Gson gson = new Gson();
        String content = gson.toJson(refundData);
        refundInfo.setContentReturn(content);
    }

}
