package cn.jiawen.payment.enums.wxpay;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum WxApiType {

	/**
	 * 商户进件提交申请单
	 */
	APPLYMENT_SUBMIT("/v3/applyment4sub/applyment/"),

	/**
	 * 申请单号查询申请状态
	 */
	QUERY_APPLYMENT_STATUS_BY_NO("/v3/applyment4sub/applyment/applyment_id/%s"),

	/**
	 * 业务申请编号查询申请状态
	 */
	QUERY_APPLYMENT_STATUS_BY_CODE("/v3/applyment4sub/applyment/business_code/%s"),

	/**
	 * 查询结算账户
	 */
	QUERY_SETTLEMENT("/v3/apply4sub/sub_merchants/%s/settlement"),

	/**
	 * 修改结算账户
	 */
	MODIFY_SETTLEMENT("/v3/apply4sub/sub_merchants/%s/modify-settlement"),

	/**
	 * 查询结算账户修改申请状态
	 */
	QUERY_APPLICATION_STATUS("/v3/apply4sub/sub_merchants/%s/application/%s"),

	/**
	 * 图片上传
	 */
	MEDIA_UPLOAD("/v3/merchant/media/upload"),




	/**
	 * Native下单
	 */
	NATIVE_PAY("/v3/pay/partner/transactions/native"),

	/**
	 * 查询订单
	 */
	ORDER_QUERY_BY_NO("/v3/pay/partner/transactions/out-trade-no/%s"),

	/**
	 * 关闭订单
	 */
	CLOSE_ORDER_BY_NO("/v3/pay/partner/transactions/out-trade-no/%s/close"),

	/**
	 * 申请退款
	 */
	DOMESTIC_REFUNDS("/v3/refund/domestic/refunds"),

	/**
	 * 查询单笔退款
	 */
	DOMESTIC_REFUNDS_QUERY("/v3/refund/domestic/refunds/%s"),

	/**
	 * 申请交易账单
	 */
	TRADE_BILLS("/v3/bill/tradebill"),

	/**
	 * 申请资金账单
	 */
	FUND_FLOW_BILLS("/v3/bill/fundflowbill");


	/**
	 * 类型
	 */
	private final String type;
}
