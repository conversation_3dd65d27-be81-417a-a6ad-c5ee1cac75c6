# 沙箱环境-支付宝支付相关参数

# 应用ID,您的APPID，收款账号既是您的APPID对应支付宝账号
alipay.app-id=9021000150699204

# 商户PID,卖家支付宝账号ID
alipay.seller-id=2088721075193861

# 支付宝网关
alipay.gateway-url=https://openapi-sandbox.dl.alipaydev.com/gateway.do

# 商户私钥，您的PKCS8格式RSA2私钥
alipay.merchant-private-key=MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCEmjU1c7YpVBKfgq2nsIIdyl7j/+btjUoyDZLDXiguMTkLVKa9C8/c6nYNqqVz6BE3/S1JZ3UFde/G6ulxE2G2VYhkjW0b2GTBTtL2k239cgjOMXEZ78nlT4x0lemMy981eZy5fKrVFOwGPo2XkTELFFWRudL+6rgIuIe4VKdy2xUqWUf248+Ga3vU/4iEtEvy88gING3Ikfvqrnse63U9MMk8gJbbOoCLCYLU0bonZgiQS0bzYt8oZC0tOPLF2WEn2Rh+LNN3ilQif5PWBovd3ttRhHe91hdSXGqa/bXnI4X80WAU34Uytr2V3gkJOtyiVFd/GA51NfWu6dktxPvvAgMBAAECggEAVWyy0piUxdMvnCsnjFwdtutkbiWxs1/OaKht4AaMhBWZ1j7Wm/s8EenJ28wPgrgCP7uOrTdFIBt2OQOlF/+Qd1Jq57xkigfCAPay2b7ooNoR6iPX5QmhW2T/6yuYpefBCEYsPSKfO16bHvq1ZOtYWPbkmVjJIQg9PwECDjIbHdYk8o9/os8hfN36Z/BcseRwePAY6InOlvjuPG4AzuQ2zZVUqxxHrghHJahOWz/dJVhR4qX7IZp9A6ybu34uR1qf76tETp79IPr9jppFbim/ZsRuBEHfmwDvrp+EjedKaSXvLlvA2+VWob5BBt+BpQWUs12T9S28mkvTU7zIh9L2gQKBgQD6tW60jdn/RENHeWavoxNmE1wyiaOvoFBYNa2qNL+3IvG30osqCvzV0hN8AZ8coQ4bXrOmOHgBy4d3Z2XOYJ4hiCZ8oK87aOGpRyZUnxXyIr0KNveQ0w/pyR1mjG+5PJIcZxWJzuvfHhdlCSeh89oNeaY2BSdtopVXhd+SSneIfwKBgQCHZqb9ubbdBBgzi7lqlc3NRYcg7R9cbvvUU/AGXWtTeLC1S31yxBDQcylfQedBi+/uMcNMYKio79rxb88OiIommqhftD0tVxrEQ+iDurEqxCfTuA5SW0jj4TIX77qWh8krkN17XymZLtwcAUw9fB9WZJkIskWBjyy5CUqGHTlUkQKBgFIoxQfMeMVLtJSmtz0VvxSzgYJaUtcKPWo65RejQ7SbcgVgrRRxF1xnBNWdnmv+Z6YWid8OIF3OOi2qJ9XTGd85RAuRiPt6KoGT/H68x69bLQI88zJ1yIA/1S+3FCHrNKK0MX3A3tF2hgopTuCnDi1eUVbgp4QpRLGzAIprdvK3AoGASLYtky+6LbJXNMciCUTtQgDGcj4ki0TmY723thNQ4YB8lL83XPxBc0+Etizz/VUiwa1jjY6SxDYaQ3GhpdJZLiX7qXlnKQQP0boOET+eiLLSGHZM6pku6YEzn3sTnmbI92ZSKkCOXD634SopQRN42LIpJGxnuw2iHLQKmz7yXKECgYEA9pC7t0DvyHVzYB7svfkrqo8N0ovp6nhRjjmIm6XYAICeCTJR5PWM7MhMUH2ds+w4/KELn5PRhpaHnVBQtbZFU17iNl0keMYrGPWHDAFVSh4M86Y80/3I5x5kda6ttHdpbnyXj0SfovAyfEGeQ29UH4KW6est+yVqNxkyKL14nFU=

# 支付宝公钥,查看地址：https://openhome.alipay.com/platform/keyManage.htm 对应APPID下的支付宝公钥
alipay.alipay-public-key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgMx5SZfkaksxfgjeN/l9bkvKjzezqrfKhwZrI+XSnDD8WLA3HdVW07rJmlMQvYnpj8IccDYTbkLL0qoGhF8yLlYQFMQCpS0NQgoz0Yk1lAxdui6YDGhBbvjQmLEdi9/k6yrDaTtCq2837OGBhojIloUW1/aHOg5vGISHeV8rJO6/0JlKpXgF38fB7sewS1yk7sNFNJRzMZMy1A6+pv/D4mLQO8N2QmHiG1Q3Snyrp5A0xlGutNwROHyGEAqsAm+DT5SGngAFP6hB2Tcs5j4ZrlVJ/x92V3UvAyK2erwHt0+u78a0vE9n7nu84K9zePebS4BsVb7Jnsq4jcBmXVVHIQIDAQAB

# 接口内容加密秘钥，对称秘钥
alipay.content-key=I7dGI2MJjB3ro8nViQ2TAQ==

# 页面跳转同步通知页面路径
alipay.return-url=http://localhost:63342/payment/payment-demo/ui/demo.html

# 服务器异步通知页面路径  需http://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问
# 注意：每次重新启动ngrok，都需要根据实际情况修改这个配置
alipay.notify-url=http://v9ab8494.natappfree.cc/api/ali-pay/trade/notify





