# 支付宝支付相关参数

# 应用ID,您的APPID，收款账号既是您的APPID对应支付宝账号   支付宝开放平台 https://open.alipay.com/develop/pm/sub/appinfo?appId=2021005183600032
alipay.app-id=

# 商户PID,卖家支付宝账号ID   支付宝商家中心收单账号：https://b.alipay.com/page/store-management/infomanage
alipay.seller-id=

# 支付宝网关
alipay.gateway-url=https://openapi.alipay.com/gateway.do

# 商户私钥，您的PKCS8格式RSA2私钥
alipay.merchant-private-key=

# 支付宝公钥,查看地址：https://open.alipay.com/develop/pm/sub/setting?appId=2021005183600032 接口加签方式对应APPID下的支付宝公钥
alipay.alipay-public-key=

# 接口内容加密秘钥，对称秘钥
alipay.content-key=


# 页面跳转同步通知页面路径
alipay.return-url=

# 服务器异步通知页面路径  需http://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问
# 注意：每次重新启动ngrok，都需要根据实际情况修改这个配置
alipay.notify-url=https://a863-180-174-204-169.ngrok.io/api/ali-pay/trade/notify





