package cn.jiawen.payment;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
@Slf4j
public class AlipayTests {

    @Resource
    private Environment config;

    @Test
    public void testAlipayConfig() {
        log.info("测试==========={}", config.getProperty("alipay.app-id"));
    }

}
