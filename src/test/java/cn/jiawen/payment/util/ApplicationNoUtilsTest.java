package cn.jiawen.payment.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 业务申请编号工具类测试
 *
 * <AUTHOR>
 * @since 1.0
 */
@SpringBootTest
@Slf4j
public class ApplicationNoUtilsTest {

    @Test
    public void testIsValidFormat() {
        // 测试有效格式
        assertTrue(ApplicationNoUtils.isValidFormat("MERCHANT123_20240820143000_001"));
        assertTrue(ApplicationNoUtils.isValidFormat("ABC_123"));
        assertTrue(ApplicationNoUtils.isValidFormat("merchant_id_123"));
        assertTrue(ApplicationNoUtils.isValidFormat("123456"));
        assertTrue(ApplicationNoUtils.isValidFormat("TEST_APPLICATION_001"));

        // 测试无效格式
        assertFalse(ApplicationNoUtils.isValidFormat("MERCHANT-123")); // 包含连字符
        assertFalse(ApplicationNoUtils.isValidFormat("MERCHANT@123")); // 包含特殊字符
        assertFalse(ApplicationNoUtils.isValidFormat("MERCHANT 123")); // 包含空格
        assertFalse(ApplicationNoUtils.isValidFormat("商户123")); // 包含中文
        assertFalse(ApplicationNoUtils.isValidFormat("")); // 空字符串
        assertFalse(ApplicationNoUtils.isValidFormat(null)); // null值
        assertFalse(ApplicationNoUtils.isValidFormat("   ")); // 只有空格
    }

    @Test
    public void testgenerateApplicationNo() {
        // 测试固定前缀生成
        String businessNo = ApplicationNoUtils.generateApplicationNo();
        log.info("生成的业务申请编号: {}", businessNo);

        assertNotNull(businessNo);
        assertTrue(businessNo.startsWith("1725024967_"));
        assertTrue(ApplicationNoUtils.isValidFormat(businessNo));
        assertTrue(ApplicationNoUtils.isValidBusinessApplicationNo(businessNo));
        assertTrue(ApplicationNoUtils.hasFixedPrefix(businessNo));
        assertEquals("1725024967", ApplicationNoUtils.extractPrefix(businessNo));
    }

    @Test
    public void testGetFixedPrefix() {
        String fixedPrefix = ApplicationNoUtils.getFixedPrefix();
        log.info("固定前缀: {}", fixedPrefix);

        assertEquals("1725024967", fixedPrefix);
    }

    @Test
    public void testHasFixedPrefix() {
        String businessNo = ApplicationNoUtils.generateApplicationNo();

        assertTrue(ApplicationNoUtils.hasFixedPrefix(businessNo));
        assertFalse(ApplicationNoUtils.hasFixedPrefix("OTHER_PREFIX_20240820_001"));
        assertFalse(ApplicationNoUtils.hasFixedPrefix(null));
    }

    @Test
    public void testExtractPrefix() {
        String businessNo = ApplicationNoUtils.generateApplicationNo();

        assertEquals("1725024967", ApplicationNoUtils.extractPrefix(businessNo));
        assertNull(ApplicationNoUtils.extractPrefix("NOSEPARATOR"));
        assertNull(ApplicationNoUtils.extractPrefix(""));
        assertNull(ApplicationNoUtils.extractPrefix(null));
    }

    @Test
    public void testIsValidBusinessApplicationNo() {
        // 测试有效的业务申请编号
        String validNo = ApplicationNoUtils.generateApplicationNo();
        assertTrue(ApplicationNoUtils.isValidBusinessApplicationNo(validNo));

        // 测试无效的业务申请编号
        assertFalse(ApplicationNoUtils.isValidBusinessApplicationNo("INVALID-FORMAT"));
        assertFalse(ApplicationNoUtils.isValidBusinessApplicationNo("OTHER_PREFIX_20240820_001"));
        assertFalse(ApplicationNoUtils.isValidBusinessApplicationNo(""));
        assertFalse(ApplicationNoUtils.isValidBusinessApplicationNo(null));
    }

    @Test
    public void testGenerateWithCustomRandomDigits() {
        // 测试不同的随机数位数
        String no1 = ApplicationNoUtils.generateApplicationNo(1);
        String no5 = ApplicationNoUtils.generateApplicationNo(5);
        String no10 = ApplicationNoUtils.generateApplicationNo(10);

        log.info("1位随机数: {}", no1);
        log.info("5位随机数: {}", no5);
        log.info("10位随机数: {}", no10);

        assertTrue(ApplicationNoUtils.isValidFormat(no1));
        assertTrue(ApplicationNoUtils.isValidFormat(no5));
        assertTrue(ApplicationNoUtils.isValidFormat(no10));
        assertTrue(ApplicationNoUtils.hasFixedPrefix(no1));
        assertTrue(ApplicationNoUtils.hasFixedPrefix(no5));
        assertTrue(ApplicationNoUtils.hasFixedPrefix(no10));

        // 测试无效的随机数位数
        assertThrows(IllegalArgumentException.class, () -> {
            ApplicationNoUtils.generateApplicationNo(0);
        });

        assertThrows(IllegalArgumentException.class, () -> {
            ApplicationNoUtils.generateApplicationNo(11);
        });
    }

    @Test
    public void testUniqueness() {
        // 生成多个编号，验证唯一性
        String no1 = ApplicationNoUtils.generateApplicationNo();
        String no2 = ApplicationNoUtils.generateApplicationNo();
        String no3 = ApplicationNoUtils.generateApplicationNo();

        log.info("编号1: {}", no1);
        log.info("编号2: {}", no2);
        log.info("编号3: {}", no3);

        // 由于包含时间戳和随机数，应该是不同的
        assertNotEquals(no1, no2);
        assertNotEquals(no2, no3);
        assertNotEquals(no1, no3);

        // 都应该有固定前缀
        assertTrue(ApplicationNoUtils.hasFixedPrefix(no1));
        assertTrue(ApplicationNoUtils.hasFixedPrefix(no2));
        assertTrue(ApplicationNoUtils.hasFixedPrefix(no3));
    }
}
