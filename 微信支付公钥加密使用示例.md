# 微信支付公钥加密使用示例

## 1. 加密工具类说明

已创建 `WxPayEncryptUtil` 工具类，提供微信支付公钥加密功能：

### 核心方法
```java
// 基础加密方法
public static String rsaEncryptOAEP(String message, PublicKey publicKey)

// 便捷加密方法
public static String encryptSensitiveInfo(String sensitiveInfo, PublicKey publicKey)

// 长度验证方法
public static boolean isValidLength(String message)
```

## 2. 在WxPayConfig中的集成

已在配置类中添加便捷方法：
```java
// 获取公钥ID
@Bean(name = "wxPayPublicKeyId")
public String getWxPayPublicKeyId()

// 加密敏感信息
public String encryptSensitiveInfo(String sensitiveInfo)
```

## 3. 使用场景示例

### 场景1：加密用户姓名
```java
@Service
public class SomeService {
    
    @Resource
    private WxPayConfig wxPayConfig;
    
    public void processUserInfo(String realName) {
        // 加密用户真实姓名
        String encryptedName = wxPayConfig.encryptSensitiveInfo(realName);
        
        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("real_name", encryptedName);
        
        // 发送请求时需要添加公钥ID头
        httpPost.setHeader("Wechatpay-Serial", wxPayConfig.getWxPayPublicKeyId());
    }
}
```

### 场景2：加密身份证号
```java
public void processIdCard(String idCardNumber) {
    // 验证长度
    if (!WxPayEncryptUtil.isValidLength(idCardNumber)) {
        throw new IllegalArgumentException("身份证号长度超出加密限制");
    }
    
    // 加密身份证号
    String encryptedIdCard = wxPayConfig.encryptSensitiveInfo(idCardNumber);
    
    // 使用加密后的数据
    Map<String, Object> params = new HashMap<>();
    params.put("id_card_number", encryptedIdCard);
}
```

### 场景3：批量加密
```java
public void processBatchInfo(String[] sensitiveInfos) {
    try {
        PublicKey publicKey = wxPayConfig.getPublicKey(wxPayConfig.getPublicKeyPath());
        String[] encryptedInfos = WxPayEncryptUtil.encryptSensitiveInfoBatch(sensitiveInfos, publicKey);
        
        // 处理加密后的数据
        for (String encrypted : encryptedInfos) {
            // 使用加密数据
        }
    } catch (Exception e) {
        log.error("批量加密失败", e);
    }
}
```

## 4. 在支付接口中的应用

### 修改WxPayServiceImpl示例
```java
// 如果某个支付接口需要加密敏感信息
public String createOrderWithSensitiveInfo(Long productId, String realName, String idCard) {
    
    // 加密敏感信息
    String encryptedName = wxPayConfig.encryptSensitiveInfo(realName);
    String encryptedIdCard = wxPayConfig.encryptSensitiveInfo(idCard);
    
    // 构建请求参数
    Map<String, Object> paramsMap = new HashMap<>();
    paramsMap.put("out_trade_no", orderNo);
    paramsMap.put("total", totalFee);
    paramsMap.put("real_name", encryptedName);      // 加密后的姓名
    paramsMap.put("id_card", encryptedIdCard);      // 加密后的身份证
    
    // 发送请求
    HttpPost httpPost = new HttpPost(url);
    httpPost.setEntity(new StringEntity(gson.toJson(paramsMap), "utf-8"));
    httpPost.setHeader("Accept", "application/json");
    httpPost.setHeader("Wechatpay-Serial", wxPayPublicKeyId); // 必须添加公钥ID
    
    // 执行请求
    CloseableHttpResponse response = wxPayClient.execute(httpPost);
}
```

## 5. 重要注意事项

### 5.1 长度限制
```java
// RSA 2048位密钥 + OAEP填充，明文最大长度约214字节
String longText = "很长的文本...";
if (!WxPayEncryptUtil.isValidLength(longText)) {
    // 需要分段加密或使用其他方案
}
```

### 5.2 字符编码
```java
// 工具类内部使用UTF-8编码
byte[] data = message.getBytes("utf-8");
```

### 5.3 异常处理
```java
try {
    String encrypted = wxPayConfig.encryptSensitiveInfo(sensitiveInfo);
} catch (IllegalBlockSizeException e) {
    // 数据长度超限
} catch (RuntimeException e) {
    // 其他加密错误
}
```

## 6. 测试验证

### 6.1 单元测试示例
```java
@Test
public void testEncryption() {
    String plainText = "测试敏感信息";
    String encrypted = wxPayConfig.encryptSensitiveInfo(plainText);
    
    assertNotNull(encrypted);
    assertNotEquals(plainText, encrypted);
    assertTrue(encrypted.length() > 0);
}
```

### 6.2 长度测试
```java
@Test
public void testLengthValidation() {
    String shortText = "短文本";
    String longText = "很长的文本".repeat(100);
    
    assertTrue(WxPayEncryptUtil.isValidLength(shortText));
    assertFalse(WxPayEncryptUtil.isValidLength(longText));
}
```

## 7. 常见问题

### Q: 什么时候需要加密？
A: 只有涉及敏感信息的接口才需要加密，如实名认证、身份验证等。

### Q: 加密后还需要添加Wechatpay-Serial头吗？
A: 是的，必须添加，告知微信支付使用对应的私钥解密。

### Q: 所有参数都需要加密吗？
A: 不是，只有敏感信息需要加密，普通业务参数保持明文。

### Q: 加密失败怎么办？
A: 检查公钥文件、数据长度、字符编码等，查看详细错误日志。
